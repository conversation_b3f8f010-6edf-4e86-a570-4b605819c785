/// 🧠 Smart Payment WebView
/// 
/// WebView thông minh có thể chuyển đổi giữa Direct và Web-based payment
/// dựa trên PaymentFlowToggle settings
library;

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../services/payment_flow_toggle.dart';
import '../../../widgets/payment_debug_panel.dart';
import '../../../core/constants/payment_constants.dart';

/// Smart Payment WebView với toggle functionality
class SmartPaymentWebView extends StatefulWidget {
  final String htmlData;
  final String baseUrl;
  final Function(String) onPaymentMethodSelected;
  final Function(Map<String, dynamic>) onPaymentResult;
  final Function()? onPaymentSuccess;
  final Function(String)? onPaymentError;

  const SmartPaymentWebView({
    super.key,
    required this.htmlData,
    required this.baseUrl,
    required this.onPaymentMethodSelected,
    required this.onPaymentResult,
    this.onPaymentSuccess,
    this.onPaymentError,
  });

  @override
  State<SmartPaymentWebView> createState() => _SmartPaymentWebViewState();
}

class _SmartPaymentWebViewState extends State<SmartPaymentWebView> {
  late WebViewController _controller;
  PaymentFlowType _currentFlowType = PaymentFlowType.auto;
  bool _isDirectPaymentEnabled = false;

  @override
  void initState() {
    super.initState();
    _initializePaymentFlow();
    _setupWebView();
  }

  Future<void> _initializePaymentFlow() async {
    final flowType = await PaymentFlowToggle.getCurrentFlowType();
    final isDirectEnabled = await PaymentFlowToggle.isDirectPaymentEnabled();
    
    setState(() {
      _currentFlowType = flowType;
      _isDirectPaymentEnabled = isDirectEnabled;
    });
    
    debugPrint('🧠 Smart Payment initialized: ${flowType.displayName}');
    debugPrint('💳 Direct payment enabled: $isDirectEnabled');
  }

  void _setupWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: _handleNavigationRequest,
          onPageStarted: _onPageStarted,
          onPageFinished: _onPageFinished,
        ),
      )
      ..loadHtmlString(widget.htmlData, baseUrl: widget.baseUrl);
  }

  /// Handle navigation request với smart routing
  Future<NavigationDecision> _handleNavigationRequest(
    NavigationRequest request,
  ) async {
    final url = request.url;
    debugPrint('🌐 Navigation request: $url');

    // Check for payment URLs
    final paymentInfo = _parsePaymentUrl(url);
    if (paymentInfo != null) {
      return await _handlePaymentUrl(url, paymentInfo);
    }

    return NavigationDecision.navigate;
  }

  /// Parse payment URL to extract provider info
  PaymentInfo? _parsePaymentUrl(String url) {
    if (url.contains('momo.vn') || url.contains('payment.momo')) {
      return PaymentInfo(provider: 'momo', url: url);
    } else if (url.contains('gateway.zalopay.vn')) {
      return PaymentInfo(provider: 'zalopay', url: url);
    } else if (url.contains('airpay.vn')) {
      return PaymentInfo(provider: 'airpay', url: url);
    }
    return null;
  }

  /// Handle payment URL với smart routing logic
  Future<NavigationDecision> _handlePaymentUrl(
    String url,
    PaymentInfo paymentInfo,
  ) async {
    debugPrint('💳 Payment URL detected: ${paymentInfo.provider}');
    
    // Notify payment method selection
    widget.onPaymentMethodSelected(paymentInfo.provider);
    
    // Check current flow type
    if (_isDirectPaymentEnabled) {
      debugPrint('🚀 Using DIRECT payment flow');
      return await _handleDirectPayment(url, paymentInfo);
    } else {
      debugPrint('🌐 Using WEB-BASED payment flow');
      return await _handleWebBasedPayment(url, paymentInfo);
    }
  }

  /// Handle direct payment flow
  Future<NavigationDecision> _handleDirectPayment(
    String url,
    PaymentInfo paymentInfo,
  ) async {
    try {
      // Generate direct payment URL
      final directUrl = _generateDirectPaymentUrl(paymentInfo);
      
      // Check if payment app is available
      if (await canLaunchUrl(Uri.parse(directUrl))) {
        debugPrint('✅ Launching direct payment: $directUrl');
        
        await launchUrl(
          Uri.parse(directUrl),
          mode: LaunchMode.externalApplication,
        );
        
        // Track direct payment attempt
        PaymentFlowEvents.logDirectPaymentAttempt(paymentInfo.provider);
        
        return NavigationDecision.prevent;
      } else {
        debugPrint('⚠️ Payment app not available, falling back to web');
        
        // Fallback to web-based payment
        PaymentFlowEvents.logWebPaymentFallback(
          paymentInfo.provider,
          'Payment app not installed',
        );
        
        return NavigationDecision.navigate;
      }
    } catch (e) {
      debugPrint('❌ Direct payment failed: $e');
      
      // Fallback to web-based payment
      PaymentFlowEvents.logWebPaymentFallback(
        paymentInfo.provider,
        'Direct payment error: $e',
      );
      
      return NavigationDecision.navigate;
    }
  }

  /// Handle web-based payment flow
  Future<NavigationDecision> _handleWebBasedPayment(
    String url,
    PaymentInfo paymentInfo,
  ) async {
    debugPrint('🌐 Processing web-based payment: ${paymentInfo.provider}');
    
    // For web-based payment, we let the WebView navigate normally
    // but we can still launch external apps if needed
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
      return NavigationDecision.prevent;
    }
    
    return NavigationDecision.navigate;
  }

  /// Generate direct payment URL for each provider
  String _generateDirectPaymentUrl(PaymentInfo paymentInfo) {
    final uri = Uri.parse(paymentInfo.url);
    
    switch (paymentInfo.provider) {
      case 'momo':
        return _generateMomoDirectUrl(uri);
      case 'zalopay':
        return _generateZaloPayDirectUrl(uri);
      case 'airpay':
        return _generateAirPayDirectUrl(uri);
      default:
        throw UnsupportedError('Unsupported payment provider: ${paymentInfo.provider}');
    }
  }

  String _generateMomoDirectUrl(Uri uri) {
    final params = {
      'action': 'payment',
      'orderId': uri.queryParameters['orderId'] ?? '',
      'amount': uri.queryParameters['amount'] ?? '',
      'description': uri.queryParameters['description'] ?? 'Beta Cinema Payment',
      'callbackScheme': 'betacineplexx',
      'callbackHost': 'momo',
    };
    
    final queryString = params.entries
        .where((e) => e.value.isNotEmpty)
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return 'momo://payment?$queryString';
  }

  String _generateZaloPayDirectUrl(Uri uri) {
    final params = {
      'action': 'payment',
      'appTransId': uri.queryParameters['appTransId'] ?? '',
      'amount': uri.queryParameters['amount'] ?? '',
      'description': uri.queryParameters['description'] ?? 'Beta Cinema Payment',
      'callbackScheme': 'betacineplexx',
      'callbackHost': 'zalopay',
    };
    
    final queryString = params.entries
        .where((e) => e.value.isNotEmpty)
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return 'zalopay://payment?$queryString';
  }

  String _generateAirPayDirectUrl(Uri uri) {
    final params = {
      'action': 'payment',
      'order_id': uri.queryParameters['order_id'] ?? '',
      'amount': uri.queryParameters['amount'] ?? '',
      'description': uri.queryParameters['description'] ?? 'Beta Cinema Payment',
      'callbackScheme': 'betacineplexx',
      'callbackHost': 'airpay',
    };
    
    final queryString = params.entries
        .where((e) => e.value.isNotEmpty)
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return 'airpay://payment?$queryString';
  }

  void _onPageStarted(String url) {
    debugPrint('📄 Page started: $url');
  }

  void _onPageFinished(String url) {
    debugPrint('✅ Page finished: $url');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        actions: [
          // Flow type indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: _getFlowColor().withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _getFlowColor()),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(_getFlowIcon(), size: 16, color: _getFlowColor()),
                const SizedBox(width: 4),
                Text(
                  _currentFlowType.key.toUpperCase(),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: _getFlowColor(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          
          // Debug panel overlay
          const PaymentDebugPanel(),
        ],
      ),
    );
  }

  Color _getFlowColor() {
    return switch (_currentFlowType) {
      PaymentFlowType.direct => Colors.green,
      PaymentFlowType.webBased => Colors.blue,
      PaymentFlowType.auto => Colors.orange,
    };
  }

  IconData _getFlowIcon() {
    return switch (_currentFlowType) {
      PaymentFlowType.direct => Icons.flash_on,
      PaymentFlowType.webBased => Icons.web,
      PaymentFlowType.auto => Icons.auto_mode,
    };
  }
}

/// Payment info class
class PaymentInfo {
  final String provider;
  final String url;
  
  PaymentInfo({
    required this.provider,
    required this.url,
  });
}
