import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'common.dart';

// Generate mocks for testing
@GenerateMocks([http.Client])

/// Comprehensive Login Page Testing Suite
///
/// This test suite covers:
/// 1. UI Element Validation
/// 2. Form Validation Testing
/// 3. Login Flow Testing (Success/Failure scenarios)
/// 4. Captcha Integration Testing
/// 5. Social Login Testing (Facebook/Apple)
/// 6. Navigation Testing
void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Login Page Tests', () {
    testWidgets('UI Elements Validation - Kiểm tra các thành phần giao diện', (WidgetTester tester) async {
      await initAppWidgetTest(tester);

      // Navigate to login page
      await tapButtonPump(tester, 'Tiế<PERSON> tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

      // Test 1: Verify all UI elements are present
      expect(find.byKey(const ValueKey("Đăng nhập")), findsOneWidget, reason: "Login title should be visible");
      expect(find.byKey(const ValueKey("Địa chỉ Email")), findsOneWidget, reason: "Email field should be present");
      expect(find.byKey(const ValueKey("Mật khẩu")), findsOneWidget, reason: "Password field should be present");
      expect(find.text("Ghi nhớ tài khoản"), findsOneWidget, reason: "Remember me checkbox should be visible");
      expect(find.text("Quên mật khẩu?"), findsOneWidget, reason: "Forgot password link should be visible");
      expect(find.widgetWithText(ElevatedButton, "Đăng nhập"), findsOneWidget, reason: "Login button should be present");
      expect(find.widgetWithText(TextButton, "Điều khoản dịch vụ"), findsOneWidget, reason: "Terms of service link should be visible");
      expect(find.widgetWithText(TextButton, "Điều kiện bảo mật"), findsOneWidget, reason: "Privacy policy link should be visible");
      expect(find.widgetWithText(TextButton, "Đăng ký?"), findsOneWidget, reason: "Register link should be visible");

      // Test 2: Verify social login buttons are present
      expect(find.text("Đăng nhập với Facebook"), findsOneWidget, reason: "Facebook login button should be visible");
      expect(find.text("Đăng nhập với Apple"), findsOneWidget, reason: "Apple login button should be visible");
    });

    testWidgets('Form Validation Testing - Kiểm tra validation form', (WidgetTester tester) async {
      await initAppWidgetTest(tester);

      // Navigate to login page
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

      // Test 1: Empty form validation
      await tapButtonPump(tester, 'Đăng nhập');
      expect(find.descendant(of: find.byKey(const ValueKey("Địa chỉ Email")), matching: find.text('Xin vui lòng nhập địa chỉ email')), findsOneWidget, reason: "Should show email required error");
      expect(find.descendant(of: find.byKey(const ValueKey("Mật khẩu")), matching: find.text('Xin vui lòng nhập mật khẩu')), findsOneWidget, reason: "Should show password required error");

      // Test 2: Invalid email format validation
      await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), 'invalid-email');
      await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123');
      await tapButtonPump(tester, 'Đăng nhập');
      expect(find.text('Email không hợp lệ'), findsOneWidget, reason: "Should show invalid email error");

      // Test 3: Password too short validation
      await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
      await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123');
      await tapButtonPump(tester, 'Đăng nhập');
      expect(find.text('Mật khẩu phải có ít nhất 6 ký tự'), findsOneWidget, reason: "Should show password too short error");
    });

    testWidgets('Login Flow Testing - Kiểm tra luồng đăng nhập', (WidgetTester tester) async {
      await initAppWidgetTest(tester);

      // Navigate to login page
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

      // Test 1: Login with non-existent account
      await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
      await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle(const Duration(milliseconds: 300));
      await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
      await pumpUntilFound(tester, find.text("Tài khoản <EMAIL> không tồn tại trong hệ thống. Vui lòng đăng ký mới."));

      expect(find.text("Tài khoản <EMAIL> không tồn tại trong hệ thống. Vui lòng đăng ký mới."), findsOneWidget, reason: "Should show account not found error");
      await tester.tap(find.widgetWithText(TextButton, "Thoát"));
      await tester.pumpAndSettle();

      // Test 2: Login with wrong password
      await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
      await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '*********');

      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle(const Duration(milliseconds: 300));
      await tapButtonPump(tester, 'Đăng nhập');
      await pumpUntilFound(tester, find.text("Sai mật khẩu cho tài khoản <EMAIL>"));

      expect(find.text("Sai mật khẩu cho tài khoản <EMAIL>"), findsOneWidget, reason: "Should show wrong password error");
      await tester.tap(find.widgetWithText(TextButton, "Thoát"));
      await tester.pumpAndSettle();

      // Test 3: Successful login
      await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
      await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle(const Duration(milliseconds: 300));
      await tapButtonPump(tester, 'Đăng nhập');
      await pumpUntilFound(tester, find.text("Home"));

      expect(find.text("Home"), findsOneWidget, reason: "Should navigate to home page after successful login");
    });

    testWidgets('Captcha Integration Testing - Kiểm tra tích hợp captcha', (WidgetTester tester) async {
      await initAppWidgetTest(tester);

      // Navigate to login page
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

      // Enter valid credentials
      await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
      await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

      // Tap login button - should trigger captcha
      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle(const Duration(milliseconds: 300));
      await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
      await tester.pumpAndSettle();

      // In development mode, captcha should return dummy token
      // In production, this would show captcha dialog
      // For now, we just verify the login process continues
      expect(find.text("Captcha validation failed"), findsNothing, reason: "Captcha should work in development mode");
    });

    testWidgets('Navigation Testing - Kiểm tra điều hướng', (WidgetTester tester) async {
      await initAppWidgetTest(tester);

      // Navigate to login page
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

      // Test 1: Navigate to register page
      await tester.tap(find.widgetWithText(TextButton, "Đăng ký?"));
      await tester.pumpAndSettle();
      expect(find.text("Đăng ký"), findsOneWidget, reason: "Should navigate to register page");

      // Go back to login
      await tester.pageBack();
      await tester.pumpAndSettle();

      // Test 2: Navigate to forgot password
      await tester.tap(find.text("Quên mật khẩu?"));
      await tester.pumpAndSettle();
      expect(find.text("Quên mật khẩu"), findsOneWidget, reason: "Should navigate to forgot password page");
    });

    testWidgets('Social Login Testing - Kiểm tra đăng nhập mạng xã hội', (WidgetTester tester) async {
      await initAppWidgetTest(tester);

      // Navigate to login page
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

      // Test Facebook login button exists and is tappable
      final facebookButton = find.text("Đăng nhập với Facebook");
      expect(facebookButton, findsOneWidget, reason: "Facebook login button should be present");

      // Test Apple login button exists and is tappable (iOS only)
      final appleButton = find.text("Đăng nhập với Apple");
      expect(appleButton, findsOneWidget, reason: "Apple login button should be present");

      // Note: Actual social login testing would require mocking the platform channels
      // This is beyond the scope of basic UI testing
    });

    testWidgets('Remember Me Functionality - Kiểm tra chức năng ghi nhớ', (WidgetTester tester) async {
      await initAppWidgetTest(tester);

      // Navigate to login page
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

      // Test remember me checkbox
      final rememberMeCheckbox = find.text("Ghi nhớ tài khoản");
      expect(rememberMeCheckbox, findsOneWidget, reason: "Remember me checkbox should be present");

      // Tap to toggle remember me
      await tester.tap(rememberMeCheckbox);
      await tester.pumpAndSettle();

      // Verify checkbox state changed (this would need more specific implementation)
    });
  });
}
