import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'common.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets('Login', (WidgetTester tester) async {
    await initAppWidgetTest(tester);

    await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
    await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
    await tapButtonPump(tester, 'Bắt đầu', type: TextButton);

    expect(find.byKey(const ValueKey("<PERSON><PERSON><PERSON> nhập")), findsOneWidget);
    expect(find.byKey(const ValueKey("Địa chỉ Email")), findsOneWidget);
    expect(find.byKey(const ValueKey("<PERSON>ật khẩu")), findsOneWidget);
    expect(find.text("<PERSON><PERSON> nhớ tài khoản"), findsOneWidget);
    expect(find.text("Quên mật khẩu?"), findsOneWidget);
    expect(find.widgetWithText(ElevatedButton, "Đăng nhập"), findsOneWidget);
    expect(find.widgetWithText(TextButton, "Điều khoản dịch vụ"), findsOneWidget);
    expect(find.widgetWithText(TextButton, "Điều kiện bảo mật"), findsOneWidget);
    expect(find.widgetWithText(TextButton, "Đăng ký?"), findsOneWidget);

    await tapButtonPump(tester, 'Đăng nhập');
    expect(find.descendant(of: find.byKey(const ValueKey("Địa chỉ Email")), matching: find.text('Xin vui lòng nhập địa chỉ email')), findsOneWidget);
    expect(find.descendant(of: find.byKey(const ValueKey("Mật khẩu")), matching: find.text('Xin vui lòng nhập mật khẩu')), findsOneWidget);

    await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
    await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

    await SystemChannels.textInput.invokeMethod('TextInput.hide');
    await tester.pumpAndSettle(const Duration(milliseconds: 300));
    await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
    await pumpUntilFound(tester, find.text("Tài khoản <EMAIL> không tồn tại trong hệ thống. Vui lòng đăng ký mới."));

    expect(find.text("Tài khoản <EMAIL> không tồn tại trong hệ thống. Vui lòng đăng ký mới."), findsOneWidget);
    await tester.tap(find.widgetWithText(TextButton, "Thoát"));
    await tester.pumpAndSettle();

    await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
    await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123123');

    await SystemChannels.textInput.invokeMethod('TextInput.hide');
    await tester.pumpAndSettle(const Duration(milliseconds: 300));
    await tapButtonPump(tester, 'Đăng nhập');
    await pumpUntilFound(tester, find.text("Sai mật khẩu cho tài khoản <EMAIL>"));


    expect(find.text("Sai mật khẩu cho tài khoản <EMAIL>"), findsOneWidget);
    await tester.tap(find.widgetWithText(TextButton, "Thoát"));
    await tester.pumpAndSettle();

    await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
    await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

    await SystemChannels.textInput.invokeMethod('TextInput.hide');
    await tester.pumpAndSettle(const Duration(milliseconds: 300));
    await tapButtonPump(tester, 'Đăng nhập');
    await pumpUntilFound(tester, find.text("Home"));

    expect(find.text("Home"), findsOneWidget);
  });
}
