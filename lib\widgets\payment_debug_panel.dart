/// 🛠️ Payment Debug Panel Widget
/// 
/// Widget để debug và test payment flows
/// Chỉ hiển thị trong debug mode hoặc khi được enable
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/payment_flow_toggle.dart';

/// Payment Debug Panel Widget
class PaymentDebugPanel extends StatefulWidget {
  /// Show panel even in release mode (for testing)
  final bool forceShow;
  
  /// Position of the floating button
  final Alignment alignment;
  
  const PaymentDebugPanel({
    super.key,
    this.forceShow = false,
    this.alignment = Alignment.bottomRight,
  });

  @override
  State<PaymentDebugPanel> createState() => _PaymentDebugPanelState();
}

class _PaymentDebugPanelState extends State<PaymentDebugPanel> {
  bool _isExpanded = false;
  PaymentFlowType _currentFlow = PaymentFlowType.auto;
  bool _debugMode = false;
  String _flowDescription = '';

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  Future<void> _loadCurrentSettings() async {
    final flow = await PaymentFlowToggle.getCurrentFlowType();
    final debug = await PaymentFlowToggle.isDebugModeEnabled();
    final description = await PaymentFlowToggle.getFlowDescription();
    
    if (mounted) {
      setState(() {
        _currentFlow = flow;
        _debugMode = debug;
        _flowDescription = description;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode or when forced
    if (!kDebugMode && !widget.forceShow) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 100,
      right: 16,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _isExpanded ? 320 : 56,
          height: _isExpanded ? 400 : 56,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: _isExpanded ? _buildExpandedPanel() : _buildCollapsedButton(),
        ),
      ),
    );
  }

  Widget _buildCollapsedButton() {
    return InkWell(
      onTap: () => setState(() => _isExpanded = true),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          color: _getFlowColor(),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.payment,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildExpandedPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '💳 Payment Debug',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => setState(() => _isExpanded = false),
                icon: const Icon(Icons.close),
                iconSize: 20,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Current Flow Status
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getFlowColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _getFlowColor().withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(_getFlowIcon(), color: _getFlowColor(), size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Current: ${_currentFlow.displayName}',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: _getFlowColor(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _flowDescription,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Flow Type Selection
          const Text('Select Flow Type:', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          
          ...PaymentFlowToggle.getAllFlowTypes().map((flowType) {
            return RadioListTile<PaymentFlowType>(
              title: Text(flowType.displayName),
              subtitle: Text(_getFlowSubtitle(flowType)),
              value: flowType,
              groupValue: _currentFlow,
              onChanged: (value) => _changeFlowType(value!),
              dense: true,
              contentPadding: EdgeInsets.zero,
            );
          }),
          
          const SizedBox(height: 16),
          
          // Debug Mode Toggle
          SwitchListTile(
            title: const Text('Debug Mode'),
            subtitle: const Text('Enable detailed logging'),
            value: _debugMode,
            onChanged: _toggleDebugMode,
            dense: true,
            contentPadding: EdgeInsets.zero,
          ),
          
          const SizedBox(height: 16),
          
          // Quick Actions
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _quickToggle,
                  icon: const Icon(Icons.swap_horiz, size: 16),
                  label: const Text('Quick Toggle'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _getFlowColor(),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _resetSettings,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Reset'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getFlowColor() {
    return switch (_currentFlow) {
      PaymentFlowType.direct => Colors.green,
      PaymentFlowType.webBased => Colors.blue,
      PaymentFlowType.auto => Colors.orange,
    };
  }

  IconData _getFlowIcon() {
    return switch (_currentFlow) {
      PaymentFlowType.direct => Icons.flash_on,
      PaymentFlowType.webBased => Icons.web,
      PaymentFlowType.auto => Icons.auto_mode,
    };
  }

  String _getFlowSubtitle(PaymentFlowType flowType) {
    return switch (flowType) {
      PaymentFlowType.direct => 'Fast, direct to payment app',
      PaymentFlowType.webBased => 'Traditional web-based flow',
      PaymentFlowType.auto => 'System decides automatically',
    };
  }

  Future<void> _changeFlowType(PaymentFlowType newFlow) async {
    await PaymentFlowToggle.setFlowType(newFlow);
    await _loadCurrentSettings();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Payment flow changed to: ${newFlow.displayName}'),
          backgroundColor: _getFlowColor(),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _toggleDebugMode(bool enabled) async {
    await PaymentFlowToggle.setDebugMode(enabled);
    await _loadCurrentSettings();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Debug mode ${enabled ? 'enabled' : 'disabled'}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _quickToggle() async {
    final newFlow = await PaymentFlowToggle.toggleFlow();
    await _loadCurrentSettings();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Quick toggle: ${newFlow.displayName}'),
          backgroundColor: _getFlowColor(),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _resetSettings() async {
    await PaymentFlowToggle.resetToDefault();
    await _loadCurrentSettings();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Settings reset to default'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}

/// Simple toggle button for quick access
class PaymentFlowToggleButton extends StatelessWidget {
  final VoidCallback? onPressed;
  
  const PaymentFlowToggleButton({
    super.key,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton.small(
      onPressed: onPressed ?? _handleToggle,
      backgroundColor: Colors.deepPurple,
      child: const Icon(Icons.payment, color: Colors.white),
    );
  }

  Future<void> _handleToggle() async {
    await PaymentFlowToggle.quickToggle();
  }
}

/// Extension to easily add debug panel to any screen
extension PaymentDebugExtension on Widget {
  /// Add payment debug panel overlay
  Widget withPaymentDebug({bool forceShow = false}) {
    return Stack(
      children: [
        this,
        PaymentDebugPanel(forceShow: forceShow),
      ],
    );
  }
}
