import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'common.dart';

/// 🧪 Demo Login Test - Ví dụ thực tế cho Auto Test màn đăng nhập
/// 
/// File này demo các test cases cơ bản nhất cho màn đăng nhập
/// Bạn có thể chạy file này để xem cách testing hoạt động
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('🎯 Demo Login Tests - Ví dụ thực tế', () {
    
    testWidgets('📱 Test 1: Kiểm tra UI elements có hiển thị đúng không', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test UI elements...');
      
      // Khởi tạo app
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiế<PERSON> tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      print('✅ Đã điều hướng đến màn đăng nhập');
      
      // Kiểm tra các elements có tồn tại không
      expect(find.text('Đăng nhập'), findsOneWidget, 
             reason: '❌ Không tìm thấy title "Đăng nhập"');
      print('✅ Tìm thấy title "Đăng nhập"');
      
      expect(find.byType(TextFormField), findsAtLeastNWidgets(2), 
             reason: '❌ Không tìm thấy đủ 2 text fields (email + password)');
      print('✅ Tìm thấy email và password fields');
      
      expect(find.text('Đăng nhập với Facebook'), findsOneWidget,
             reason: '❌ Không tìm thấy nút Facebook login');
      print('✅ Tìm thấy nút Facebook login');
      
      expect(find.text('Đăng nhập với Apple'), findsOneWidget,
             reason: '❌ Không tìm thấy nút Apple login');
      print('✅ Tìm thấy nút Apple login');
      
      expect(find.text('Quên mật khẩu?'), findsOneWidget,
             reason: '❌ Không tìm thấy link "Quên mật khẩu?"');
      print('✅ Tìm thấy link "Quên mật khẩu?"');
      
      print('🎉 Test UI elements PASSED!');
    });

    testWidgets('❌ Test 2: Kiểm tra validation khi form trống', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test validation form trống...');
      
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      // Thử submit form trống
      await tapButtonPump(tester, 'Đăng nhập');
      
      // Kiểm tra có hiển thị lỗi validation không
      // Note: Cần kiểm tra theo implementation thực tế của app
      print('✅ Đã submit form trống');
      print('ℹ️  Cần kiểm tra validation messages theo implementation thực tế');
      
      print('🎉 Test validation form trống COMPLETED!');
    });

    testWidgets('📧 Test 3: Kiểm tra validation email không hợp lệ', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test validation email...');
      
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      // Nhập email không hợp lệ
      final emailFields = find.byType(TextFormField);
      if (emailFields.evaluate().isNotEmpty) {
        await tester.enterText(emailFields.first, 'email-khong-hop-le');
        await tester.enterText(emailFields.last, 'password123');
        print('✅ Đã nhập email không hợp lệ: "email-khong-hop-le"');
        
        await tapButtonPump(tester, 'Đăng nhập');
        print('✅ Đã submit form với email không hợp lệ');
      }
      
      print('🎉 Test validation email COMPLETED!');
    });

    testWidgets('🔐 Test 4: Thử đăng nhập với tài khoản không tồn tại', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test đăng nhập với tài khoản không tồn tại...');
      
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      // Nhập tài khoản không tồn tại
      final emailFields = find.byType(TextFormField);
      if (emailFields.evaluate().length >= 2) {
        await tester.enterText(emailFields.first, '<EMAIL>');
        await tester.enterText(emailFields.last, 'password123');
        print('✅ Đã nhập tài khoản không tồn tại');
        
        // Ẩn bàn phím trước khi submit
        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle(const Duration(milliseconds: 300));
        
        await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
        print('✅ Đã submit form');
        
        // Đợi response từ API
        await pumpUntilFound(tester, find.textContaining("không tồn tại"), 
                             timeout: const Duration(seconds: 10));
        
        // Kiểm tra có hiển thị thông báo lỗi không
        expect(find.textContaining("không tồn tại"), findsOneWidget,
               reason: '❌ Không hiển thị thông báo lỗi cho tài khoản không tồn tại');
        print('✅ Hiển thị đúng thông báo lỗi: tài khoản không tồn tại');
        
        // Đóng dialog lỗi
        await tester.tap(find.widgetWithText(TextButton, "Thoát"));
        await tester.pumpAndSettle();
      }
      
      print('🎉 Test tài khoản không tồn tại PASSED!');
    });

    testWidgets('🔑 Test 5: Thử đăng nhập với mật khẩu sai', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test đăng nhập với mật khẩu sai...');
      
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      // Nhập tài khoản có tồn tại nhưng mật khẩu sai
      final emailFields = find.byType(TextFormField);
      if (emailFields.evaluate().length >= 2) {
        await tester.enterText(emailFields.first, '<EMAIL>');
        await tester.enterText(emailFields.last, 'matkhausai123');
        print('✅ Đã nhập tài khoản admin với mật khẩu sai');
        
        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle(const Duration(milliseconds: 300));
        
        await tapButtonPump(tester, 'Đăng nhập');
        
        // Đợi thông báo lỗi mật khẩu sai
        await pumpUntilFound(tester, find.textContaining("Sai mật khẩu"), 
                             timeout: const Duration(seconds: 10));
        
        expect(find.textContaining("Sai mật khẩu"), findsOneWidget,
               reason: '❌ Không hiển thị thông báo lỗi mật khẩu sai');
        print('✅ Hiển thị đúng thông báo lỗi: mật khẩu sai');
        
        // Đóng dialog lỗi
        await tester.tap(find.widgetWithText(TextButton, "Thoát"));
        await tester.pumpAndSettle();
      }
      
      print('🎉 Test mật khẩu sai PASSED!');
    });

    testWidgets('✅ Test 6: Đăng nhập thành công', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test đăng nhập thành công...');
      
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      // Nhập tài khoản đúng
      final emailFields = find.byType(TextFormField);
      if (emailFields.evaluate().length >= 2) {
        await tester.enterText(emailFields.first, '<EMAIL>');
        await tester.enterText(emailFields.last, '123123');
        print('✅ Đã nhập tài khoản admin với mật khẩu đúng');
        
        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle(const Duration(milliseconds: 300));
        
        await tapButtonPump(tester, 'Đăng nhập');
        
        // Đợi điều hướng đến trang Home
        await pumpUntilFound(tester, find.text("Home"), 
                             timeout: const Duration(seconds: 15));
        
        expect(find.text("Home"), findsOneWidget,
               reason: '❌ Không điều hướng đến trang Home sau khi đăng nhập thành công');
        print('✅ Đăng nhập thành công và điều hướng đến trang Home');
      }
      
      print('🎉 Test đăng nhập thành công PASSED!');
    });

    testWidgets('🔄 Test 7: Kiểm tra điều hướng "Quên mật khẩu"', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test điều hướng "Quên mật khẩu"...');
      
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      // Tap vào "Quên mật khẩu?"
      await tester.tap(find.text("Quên mật khẩu?"));
      await tester.pumpAndSettle();
      
      // Kiểm tra có điều hướng đến trang quên mật khẩu không
      expect(find.text("Quên mật khẩu"), findsOneWidget,
             reason: '❌ Không điều hướng đến trang "Quên mật khẩu"');
      print('✅ Điều hướng thành công đến trang "Quên mật khẩu"');
      
      print('🎉 Test điều hướng "Quên mật khẩu" PASSED!');
    });

    testWidgets('📝 Test 8: Kiểm tra điều hướng "Đăng ký"', 
                (WidgetTester tester) async {
      print('🚀 Bắt đầu test điều hướng "Đăng ký"...');
      
      await initAppWidgetTest(tester);
      
      // Điều hướng đến màn đăng nhập
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
      await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
      
      // Tìm và tap vào link đăng ký
      final registerLinks = find.textContaining("Đăng ký");
      if (registerLinks.evaluate().isNotEmpty) {
        await tester.tap(registerLinks.first);
        await tester.pumpAndSettle();
        
        // Kiểm tra có điều hướng đến trang đăng ký không
        expect(find.text("Đăng ký"), findsAtLeastNWidgets(1),
               reason: '❌ Không điều hướng đến trang "Đăng ký"');
        print('✅ Điều hướng thành công đến trang "Đăng ký"');
      }
      
      print('🎉 Test điều hướng "Đăng ký" PASSED!');
    });
  });
}

/// Helper function để đợi cho đến khi tìm thấy element
Future<void> pumpUntilFound(
  WidgetTester tester,
  Finder finder, {
  Duration timeout = const Duration(seconds: 5),
}) async {
  final end = DateTime.now().add(timeout);
  
  while (DateTime.now().isBefore(end)) {
    await tester.pumpAndSettle();
    
    if (finder.evaluate().isNotEmpty) {
      return;
    }
    
    await tester.pump(const Duration(milliseconds: 100));
  }
  
  throw Exception('Timeout: Could not find ${finder.description} within ${timeout.inSeconds} seconds');
}
