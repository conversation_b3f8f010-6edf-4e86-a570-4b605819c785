# 🔑 Facebook Key Hash Generator for Android (Windows PowerShell)
# Generates key hash needed for Facebook login on Android

Write-Host "🔑 =====================================" -ForegroundColor Cyan
Write-Host "🔑 FACEBOOK KEY HASH GENERATOR" -ForegroundColor Cyan
Write-Host "🔑 =====================================" -ForegroundColor Cyan
Write-Host ""

# Check if Java is available
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Java not found"
    }
} catch {
    Write-Host "❌ Java not found. Please install Java JDK." -ForegroundColor Red
    Write-Host "💡 Download from: https://www.oracle.com/java/technologies/downloads/" -ForegroundColor Yellow
    exit 1
}

Write-Host "📱 Generating Facebook key hash for Android..." -ForegroundColor Green
Write-Host ""

# Debug keystore path
$debugKeystore = "$env:USERPROFILE\.android\debug.keystore"

# Check if debug keystore exists
if (-not (Test-Path $debugKeystore)) {
    Write-Host "❌ Debug keystore not found at: $debugKeystore" -ForegroundColor Red
    Write-Host "💡 Please run 'flutter run' once to generate debug keystore" -ForegroundColor Yellow
    exit 1
}

Write-Host "🔍 Using debug keystore: $debugKeystore" -ForegroundColor Blue
Write-Host ""

# Generate key hash using Java (since OpenSSL might not be available on Windows)
Write-Host "🔄 Generating key hash..." -ForegroundColor Yellow

try {
    # Create temporary Java file to generate key hash
    $javaCode = @"
import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.cert.Certificate;
import java.util.Base64;

public class FacebookKeyHash {
    public static void main(String[] args) {
        try {
            KeyStore keystore = KeyStore.getInstance("JKS");
            keystore.load(new FileInputStream("$($debugKeystore.Replace('\', '\\'))"), "android".toCharArray());
            
            Certificate cert = keystore.getCertificate("androiddebugkey");
            MessageDigest md = MessageDigest.getInstance("SHA1");
            md.update(cert.getEncoded());
            
            String keyHash = Base64.getEncoder().encodeToString(md.digest());
            System.out.println(keyHash);
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            System.exit(1);
        }
    }
}
"@

    # Write Java code to temporary file
    $tempDir = [System.IO.Path]::GetTempPath()
    $javaFile = Join-Path $tempDir "FacebookKeyHash.java"
    $classFile = Join-Path $tempDir "FacebookKeyHash.class"
    
    $javaCode | Out-File -FilePath $javaFile -Encoding UTF8
    
    # Compile Java code
    javac $javaFile
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to compile Java code"
    }
    
    # Run Java code to generate key hash
    $keyHash = java -cp $tempDir FacebookKeyHash
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to generate key hash"
    }
    
    # Clean up temporary files
    Remove-Item $javaFile -ErrorAction SilentlyContinue
    Remove-Item $classFile -ErrorAction SilentlyContinue
    
    if ($keyHash -and $keyHash.Trim() -ne "") {
        Write-Host "✅ Key hash generated successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🔑 =====================================" -ForegroundColor Cyan
        Write-Host "🔑 YOUR FACEBOOK KEY HASH:" -ForegroundColor Cyan
        Write-Host "🔑 =====================================" -ForegroundColor Cyan
        Write-Host "🔑 $($keyHash.Trim())" -ForegroundColor Yellow
        Write-Host "🔑 =====================================" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "📋 NEXT STEPS:" -ForegroundColor Green
        Write-Host "1. Copy the key hash above" -ForegroundColor White
        Write-Host "2. Go to https://developers.facebook.com/" -ForegroundColor White
        Write-Host "3. Select app: 367174740769877" -ForegroundColor White
        Write-Host "4. Settings → Basic → Android" -ForegroundColor White
        Write-Host "5. Paste into 'Key Hashes' field" -ForegroundColor White
        Write-Host "6. Save changes" -ForegroundColor White
        Write-Host ""
        Write-Host "📱 Package Name: com.beta.betacineplex" -ForegroundColor Blue
        Write-Host "🆔 Facebook App ID: 367174740769877" -ForegroundColor Blue
        Write-Host ""
        Write-Host "✅ After adding key hash, Facebook login should work on Android!" -ForegroundColor Green
        
        # Copy to clipboard if possible
        try {
            $keyHash.Trim() | Set-Clipboard
            Write-Host ""
            Write-Host "📋 Key hash copied to clipboard!" -ForegroundColor Magenta
        } catch {
            # Clipboard not available, ignore
        }
    } else {
        throw "Empty key hash generated"
    }
    
} catch {
    Write-Host "❌ Failed to generate key hash: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Make sure you have run 'flutter run' at least once" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
