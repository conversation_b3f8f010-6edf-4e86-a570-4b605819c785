# 🧪 Hướng dẫn Auto Test cho Màn Đăng nhập Flutter

## 📚 Mục lục
1. [Tổng quan về Testing](#tổng-quan)
2. [Setup Testing Environment](#setup)
3. [Unit Tests cho Login Logic](#unit-tests)
4. [Widget Tests cho Login UI](#widget-tests)
5. [Integration Tests cho Login Flow](#integration-tests)
6. [Mocking API và Dependencies](#mocking)
7. [Chạy và Debug Tests](#chạy-tests)
8. [Best Practices](#best-practices)

## 🎯 Tổng quan về Testing {#tổng-quan}

### Tại sao cần test màn đăng nhập?
- ✅ **Bảo mật**: Đ<PERSON>m bảo validation hoạt động đúng
- ✅ **UX**: Kiểm tra user experience mượt mà
- ✅ **API Integration**: Test kết nối với backend
- ✅ **Error Handling**: Xử lý lỗi đúng cách
- ✅ **Social Login**: Facebook, Apple login hoạt động

### Các loại test cho màn đăng nhập:

| Loại Test | Mục đích | Thời gian | V<PERSON> dụ |
|-----------|----------|-----------|-------|
| **Unit Tests** | Test logic validation | ~1ms | Email format, password length |
| **Widget Tests** | Test UI components | ~100ms | Form rendering, button states |
| **Integration Tests** | Test toàn bộ flow | ~5s | Login journey end-to-end |

## ⚙️ Setup Testing Environment {#setup}

### 1. Cập nhật `pubspec.yaml`

```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.2
  build_runner: ^2.4.7
  http: ^1.1.0
  shared_preferences: ^2.2.2
  bloc_test: ^9.1.5
```

### 2. Cấu trúc thư mục test

```
test/
├── unit/
│   ├── login_validation_test.dart
│   └── auth_service_test.dart
├── widget/
│   ├── login_form_test.dart
│   └── login_buttons_test.dart
├── integration/
│   ├── login_flow_test.dart
│   └── social_login_test.dart
├── mocks/
│   ├── mock_api.dart
│   └── mock_auth_cubit.dart
└── helpers/
    ├── test_helpers.dart
    └── pump_app.dart
```

### 3. Tạo file helper chung

```dart
// test/helpers/test_helpers.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';

class TestHelpers {
  // Helper để tạo MaterialApp với dependencies
  static Widget createTestApp({
    required Widget child,
    List<BlocProvider>? providers,
    List<RepositoryProvider>? repositories,
  }) {
    return MaterialApp(
      home: MultiBlocProvider(
        providers: providers ?? [],
        child: MultiRepositoryProvider(
          providers: repositories ?? [],
          child: child,
        ),
      ),
    );
  }

  // Helper để điền form đăng nhập
  static Future<void> fillLoginForm(
    WidgetTester tester, {
    required String email,
    required String password,
  }) async {
    await tester.enterText(find.byKey(const Key('email_field')), email);
    await tester.enterText(find.byKey(const Key('password_field')), password);
    await tester.pumpAndSettle();
  }

  // Helper để tap button và đợi animation
  static Future<void> tapAndSettle(
    WidgetTester tester,
    Finder finder,
  ) async {
    await tester.tap(finder);
    await tester.pumpAndSettle();
  }
}
```

## 🧩 Unit Tests cho Login Logic {#unit-tests}

### Test Email và Password Validation

```dart
// test/unit/login_validation_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_app/utils/validators.dart';

void main() {
  group('🧪 Login Validation Unit Tests', () {
    group('Email Validation', () {
      test('✅ should accept valid email formats', () {
        // Arrange
        final validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        // Act & Assert
        for (final email in validEmails) {
          final result = Validators.isValidEmail(email);
          expect(result, isTrue, reason: '$email should be valid');
        }
      });

      test('❌ should reject invalid email formats', () {
        // Arrange
        final invalidEmails = [
          'invalid-email',
          'test@',
          '@example.com',
          '',
          '<EMAIL>',
          'test@.com',
          'test@com',
        ];

        // Act & Assert
        for (final email in invalidEmails) {
          final result = Validators.isValidEmail(email);
          expect(result, isFalse, reason: '$email should be invalid');
        }
      });
    });

    group('Password Validation', () {
      test('✅ should accept valid passwords', () {
        // Arrange
        final validPasswords = [
          '123456',
          'password123',
          'MySecurePass!',
          'abcdef',
        ];

        // Act & Assert
        for (final password in validPasswords) {
          final result = Validators.isValidPassword(password);
          expect(result, isTrue, reason: '$password should be valid');
        }
      });

      test('❌ should reject invalid passwords', () {
        // Arrange
        final invalidPasswords = [
          '12345',      // Too short
          '',           // Empty
          '   ',        // Only spaces
          'abc',        // Too short
        ];

        // Act & Assert
        for (final password in invalidPasswords) {
          final result = Validators.isValidPassword(password);
          expect(result, isFalse, reason: '$password should be invalid');
        }
      });
    });

    group('Login Request Model', () {
      test('should create login request with correct data', () {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const deviceId = 'test-device-id';

        // Act
        final loginRequest = {
          'username': email,
          'password': password,
          'deviceId': deviceId,
          'rememberMe': true,
        };

        // Assert
        expect(loginRequest['username'], equals(email));
        expect(loginRequest['password'], equals(password));
        expect(loginRequest['deviceId'], equals(deviceId));
        expect(loginRequest['rememberMe'], isTrue);
      });
    });
  });
}
```

### Test Auth Service Logic

```dart
// test/unit/auth_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_app/service/auth.dart';

@GenerateMocks([http.Client])
import 'auth_service_test.mocks.dart';

void main() {
  group('🔐 Auth Service Unit Tests', () {
    late MockClient mockClient;
    late SAuth authService;

    setUp(() {
      mockClient = MockClient();
      authService = SAuth(
        'https://api.example.com',
        {'Content-Type': 'application/json'},
        (result) async => null, // Mock checkAuth function
      );
    });

    test('should make login API call with correct parameters', () async {
      // Arrange
      final loginData = {
        'username': '<EMAIL>',
        'password': 'password123',
        'deviceId': 'test-device',
      };

      when(mockClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response('{"success": true}', 200));

      // Act
      await authService.login(body: loginData);

      // Assert
      verify(mockClient.post(
        Uri.parse('https://api.example.com/api/v1/erp/accounts/login'),
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).called(1);
    });
  });
}
```

## 🎨 Widget Tests cho Login UI {#widget-tests}

### Test Login Form Components

```dart
// test/widget/login_form_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_app/pages/access/login.dart';
import 'package:flutter_app/cubit/auth_cubit.dart';
import '../helpers/test_helpers.dart';

class MockAuthCubit extends Mock implements AuthCubit {}

void main() {
  group('🎨 Login Form Widget Tests', () {
    late MockAuthCubit mockAuthCubit;

    setUp(() {
      mockAuthCubit = MockAuthCubit();
      when(mockAuthCubit.state).thenReturn(AuthInitial());
    });

    Widget createLoginWidget() {
      return TestHelpers.createTestApp(
        child: const LoginPage(),
        providers: [
          BlocProvider<AuthCubit>.value(value: mockAuthCubit),
        ],
      );
    }

    testWidgets('🔍 should render all UI elements correctly', 
                (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Assert - Check all essential UI elements
      expect(find.text('Đăng nhập'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email + Password
      expect(find.byType(ElevatedButton), findsAtLeastNWidgets(1));
      expect(find.text('Quên mật khẩu?'), findsOneWidget);
      expect(find.text('Đăng ký'), findsOneWidget);
      expect(find.text('Đăng nhập với Facebook'), findsOneWidget);
      expect(find.text('Đăng nhập với Apple'), findsOneWidget);
    });

    testWidgets('❌ should show validation errors for empty form', 
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Act - Submit empty form
      await TestHelpers.tapAndSettle(tester, find.text('Đăng nhập'));

      // Assert - Check validation messages
      expect(find.textContaining('email'), findsAtLeastNWidgets(1));
      expect(find.textContaining('mật khẩu'), findsAtLeastNWidgets(1));
    });

    testWidgets('❌ should show error for invalid email format', 
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Act - Enter invalid email
      await TestHelpers.fillLoginForm(tester, 
        email: 'invalid-email', 
        password: 'password123'
      );
      await TestHelpers.tapAndSettle(tester, find.text('Đăng nhập'));

      // Assert
      expect(find.textContaining('email'), findsAtLeastNWidgets(1));
    });

    testWidgets('❌ should show error for short password', 
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Act - Enter short password
      await TestHelpers.fillLoginForm(tester, 
        email: '<EMAIL>', 
        password: '123'
      );
      await TestHelpers.tapAndSettle(tester, find.text('Đăng nhập'));

      // Assert
      expect(find.textContaining('6'), findsAtLeastNWidgets(1));
    });

    testWidgets('👁️ should toggle password visibility', 
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Find password field and visibility toggle
      final passwordField = find.byKey(const Key('password_field'));
      final visibilityToggle = find.byIcon(Icons.visibility);

      if (visibilityToggle.evaluate().isNotEmpty) {
        // Assert initial state (password hidden)
        TextFormField passwordWidget = tester.widget(passwordField);
        expect(passwordWidget.obscureText, isTrue);

        // Act - Toggle visibility
        await TestHelpers.tapAndSettle(tester, visibilityToggle);

        // Assert - Password now visible
        passwordWidget = tester.widget(passwordField);
        expect(passwordWidget.obscureText, isFalse);
      }
    });

    testWidgets('☑️ should toggle remember me checkbox', 
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      final rememberMeCheckbox = find.byType(Checkbox);
      
      if (rememberMeCheckbox.evaluate().isNotEmpty) {
        // Assert initial state
        Checkbox checkbox = tester.widget(rememberMeCheckbox);
        expect(checkbox.value, isFalse);

        // Act - Toggle checkbox
        await TestHelpers.tapAndSettle(tester, rememberMeCheckbox);

        // Assert - State changed
        checkbox = tester.widget(rememberMeCheckbox);
        expect(checkbox.value, isTrue);
      }
    });

    testWidgets('⏳ should show loading state during login', 
                (WidgetTester tester) async {
      // Arrange
      when(mockAuthCubit.state).thenReturn(AuthLoading());
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Assert - Loading indicator should be visible
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });
}
```

## 🔗 Integration Tests cho Login Flow {#integration-tests}

### Test Complete Login Journey

```dart
// test/integration/login_flow_test.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import '../helpers/test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('🔗 Login Flow Integration Tests', () {

    testWidgets('✅ Complete successful login journey',
                (WidgetTester tester) async {
      // Arrange - Start app
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();

      // Navigate to login page
      await _navigateToLogin(tester);

      // Act - Perform login with valid credentials
      await TestHelpers.fillLoginForm(tester,
        email: '<EMAIL>',
        password: '123123',
      );

      // Hide keyboard before submitting
      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle();

      // Submit login form
      await TestHelpers.tapAndSettle(tester, find.text('Đăng nhập'));

      // Wait for navigation to home page
      await _waitForNavigation(tester, 'Home', timeout: 10);

      // Assert - Should be on home page
      expect(find.text('Home'), findsOneWidget);
    });

    testWidgets('❌ Login with invalid credentials shows error',
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();
      await _navigateToLogin(tester);

      // Act - Login with wrong credentials
      await TestHelpers.fillLoginForm(tester,
        email: '<EMAIL>',
        password: 'wrongpassword',
      );

      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle();
      await TestHelpers.tapAndSettle(tester, find.text('Đăng nhập'));

      // Wait for error message
      await _waitForText(tester, 'không tồn tại', timeout: 10);

      // Assert - Error message displayed
      expect(find.textContaining('không tồn tại'), findsOneWidget);
    });

    testWidgets('🔄 Forgot password navigation works',
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();
      await _navigateToLogin(tester);

      // Act - Tap forgot password
      await TestHelpers.tapAndSettle(tester, find.text('Quên mật khẩu?'));

      // Assert - Navigate to forgot password page
      expect(find.text('Quên mật khẩu'), findsOneWidget);
    });

    testWidgets('📝 Register navigation works',
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();
      await _navigateToLogin(tester);

      // Act - Tap register link
      await TestHelpers.tapAndSettle(tester, find.text('Đăng ký'));

      // Assert - Navigate to register page
      expect(find.text('Đăng ký'), findsOneWidget);
    });

    testWidgets('📱 Social login buttons are functional',
                (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();
      await _navigateToLogin(tester);

      // Test Facebook login button
      final facebookButton = find.text('Đăng nhập với Facebook');
      expect(facebookButton, findsOneWidget);
      await TestHelpers.tapAndSettle(tester, facebookButton);

      // Test Apple login button
      final appleButton = find.text('Đăng nhập với Apple');
      expect(appleButton, findsOneWidget);
      await TestHelpers.tapAndSettle(tester, appleButton);

      // Note: Actual social login testing requires platform channel mocking
    });
  });
}

// Helper functions
Future<void> _navigateToLogin(WidgetTester tester) async {
  await TestHelpers.tapAndSettle(tester, find.text('Tiếp tục'));
  await TestHelpers.tapAndSettle(tester, find.text('Tiếp tục'));
  await TestHelpers.tapAndSettle(tester, find.text('Bắt đầu'));
}

Future<void> _waitForNavigation(WidgetTester tester, String text,
                               {int timeout = 5}) async {
  await tester.pumpAndSettle();
  for (int i = 0; i < timeout; i++) {
    if (find.text(text).evaluate().isNotEmpty) return;
    await tester.pump(const Duration(seconds: 1));
  }
}

Future<void> _waitForText(WidgetTester tester, String text,
                         {int timeout = 5}) async {
  for (int i = 0; i < timeout; i++) {
    await tester.pumpAndSettle();
    if (find.textContaining(text).evaluate().isNotEmpty) return;
    await tester.pump(const Duration(seconds: 1));
  }
}
```

## 🎭 Mocking API và Dependencies {#mocking}

### Mock API Service

```dart
// test/mocks/mock_api.dart
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_app/service/index.dart';
import 'package:flutter_app/models/index.dart';

@GenerateMocks([Api, SAuth])
class MockApi extends Mock implements Api {
  @override
  SAuth get auth => MockSAuth();
}

class MockSAuth extends Mock implements SAuth {
  @override
  Future<MApi?> login({required body, String? captchaToken}) async {
    // Mock successful login
    if (body['username'] == '<EMAIL>' && body['password'] == '123123') {
      return MApi(
        isSuccess: true,
        data: {'token': 'mock_token', 'userId': '123'},
        message: 'Login successful',
      );
    }

    // Mock failed login
    return MApi(
      isSuccess: false,
      data: null,
      message: 'Tài khoản ${body['username']} không tồn tại trong hệ thống',
    );
  }
}
```

## 🚀 Chạy và Debug Tests {#chạy-tests}

### Commands để chạy tests

```bash
# 1. Chạy tất cả unit và widget tests
flutter test

# 2. Chạy test cụ thể
flutter test test/unit/login_validation_test.dart

# 3. Chạy integration tests
flutter test integration_test/login_flow_test.dart

# 4. Chạy tests với coverage
flutter test --coverage

# 5. Chạy tests và xem kết quả chi tiết
flutter test --reporter=expanded

# 6. Chạy tests trên device thật
flutter test integration_test/login_flow_test.dart -d <device_id>
```

### Debug Tests

```dart
// Thêm breakpoints và debug info
testWidgets('debug login test', (WidgetTester tester) async {
  await tester.pumpWidget(createLoginWidget());

  // Print widget tree để debug
  debugDumpApp();

  // Print tất cả widgets tìm thấy
  print('Found widgets: ${find.byType(TextFormField).evaluate().length}');

  // Chụp screenshot
  await tester.binding.convertFlutterSurfaceToImage();
});
```

## 📋 Best Practices {#best-practices}

### 1. Tổ chức Test Code

```dart
// ✅ Good: Descriptive test names
testWidgets('should show error when email is invalid', (tester) async {
  // Test implementation
});

// ❌ Bad: Vague test names
testWidgets('test email', (tester) async {
  // Test implementation
});
```

### 2. AAA Pattern (Arrange-Act-Assert)

```dart
testWidgets('should validate email format', (WidgetTester tester) async {
  // Arrange - Setup test data
  const invalidEmail = 'invalid-email';
  await tester.pumpWidget(createLoginWidget());

  // Act - Perform action
  await tester.enterText(find.byKey(Key('email_field')), invalidEmail);
  await tester.tap(find.text('Đăng nhập'));
  await tester.pumpAndSettle();

  // Assert - Verify result
  expect(find.text('Invalid email format'), findsOneWidget);
});
```

### 3. Test Data Management

```dart
class TestData {
  static const validEmail = '<EMAIL>';
  static const validPassword = 'password123';
  static const invalidEmail = 'invalid-email';
  static const shortPassword = '123';

  static const loginCredentials = {
    'admin': {'email': '<EMAIL>', 'password': '123123'},
    'user': {'email': '<EMAIL>', 'password': 'password'},
  };
}
```

### 4. Cleanup và Setup

```dart
group('Login Tests', () {
  late MockApi mockApi;

  setUp(() {
    mockApi = MockApi();
    // Setup before each test
  });

  tearDown(() {
    // Cleanup after each test
    reset(mockApi);
  });

  setUpAll(() {
    // Setup once before all tests
  });

  tearDownAll(() {
    // Cleanup once after all tests
  });
});
```

### 5. Performance Testing

```dart
testWidgets('login performance test', (WidgetTester tester) async {
  final stopwatch = Stopwatch()..start();

  await tester.pumpWidget(createLoginWidget());
  await _performLogin(tester);

  stopwatch.stop();

  // Assert performance requirements
  expect(stopwatch.elapsedMilliseconds, lessThan(2000));
  print('Login completed in ${stopwatch.elapsedMilliseconds}ms');
});
```

## 📊 Test Coverage và Reporting

### Xem Coverage Report

```bash
# Generate coverage
flutter test --coverage

# Install lcov (Linux/Mac)
sudo apt-get install lcov

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# Open report
open coverage/html/index.html
```

### CI/CD Integration

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter test
      - run: flutter test integration_test/
```

---

## 🎯 Kết luận

Auto testing cho màn đăng nhập giúp:
- ✅ Đảm bảo chất lượng code
- ✅ Phát hiện bug sớm
- ✅ Tự tin khi refactor
- ✅ Tài liệu sống cho code

**Nhớ**: Test không phải để tìm bug, mà để ngăn chặn bug!

Happy Testing! 🧪✨
```
