# 🚀 Facebook Login Quick Fix - Android Key Hash

## 🚨 **Vấn đề**
- ✅ **iOS**: Facebook login hoạt động bình thường
- ❌ **Android**: Lỗi "Invalid key hash"

## ⚡ **Quick Fix (3 bước)**

### **Bước 1: Generate Key Hash**

#### **Option A: Sử dụng Script (Recommended)**

**Windows:**
```powershell
# Chạy PowerShell script
.\generate_facebook_key_hash.ps1
```

**Mac/Linux:**
```bash
# Chạy bash script
chmod +x generate_facebook_key_hash.sh
./generate_facebook_key_hash.sh
```

#### **Option B: Manual Command**

```bash
# Terminal command
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android | openssl sha1 -binary | openssl base64
```

### **Bước 2: Copy Key Hash**

Script sẽ output key hash như này:
```
🔑 =====================================
🔑 YOUR FACEBOOK KEY HASH:
🔑 =====================================
🔑 M/51abc123def456ghi789jkl...
🔑 =====================================
```

**Copy key hash này!**

### **Bước 3: Update Facebook Console**

1. **Truy cập:** https://developers.facebook.com/
2. **Chọn app:** `367174740769877`
3. **Settings** → **Basic** → **Android**
4. **Paste key hash** vào "Key Hashes" field
5. **Save Changes**

## ✅ **Test Facebook Login**

Sau khi update key hash:

```dart
// Test login
final result = await FacebookAuth.instance.login(
  permissions: ['public_profile', 'email']
);

// Expected result:
// ✅ LoginStatus.success
// ✅ User data retrieved
// ✅ No "Invalid key hash" error
```

## 🔍 **Troubleshooting**

### **Lỗi: "keytool not found"**
- **Windows**: Install Java JDK
- **Mac**: `brew install openjdk`
- **Linux**: `sudo apt install openjdk-11-jdk`

### **Lỗi: "debug.keystore not found"**
```bash
# Run Flutter once để generate keystore
flutter run
```

### **Key hash vẫn invalid**
- Generate multiple key hashes (debug + release)
- Add tất cả vào Facebook Console
- Verify package name: `com.beta.betacineplex`

## 📋 **Configuration Summary**

| Setting | Value | Status |
|---------|-------|---------|
| **Facebook App ID** | `367174740769877` | ✅ CORRECT |
| **Package Name** | `com.beta.betacineplex` | ✅ CORRECT |
| **Key Hash** | ❌ **MISSING** | 🔧 **NEEDS FIX** |

## 🎯 **Expected Result**

**Before Fix:**
```
❌ Invalid key hash. The key hash M/51... does not match any stored key hashes
```

**After Fix:**
```
✅ Facebook login successful
✅ User data: {name: "John Doe", email: "<EMAIL>"}
```

## 💡 **Why This Happens**

- **iOS**: Uses Bundle ID only, no key hash needed
- **Android**: Requires both package name AND key hash for security
- **Facebook**: Validates Android apps using SHA1 signature hash

**This fix will resolve Facebook login on Android completely!** 🎉

---

## 🚀 **Quick Commands**

```bash
# Generate key hash (Mac/Linux)
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android | openssl sha1 -binary | openssl base64

# Test Facebook login
flutter run
# Then test Facebook login button in app
```

**Facebook Developer Console:** https://developers.facebook.com/apps/367174740769877/settings/basic/
