part of 'my_account_info.dart';

class _AccountInfo extends StatefulWidget {
  final MUser user;
  final Function(dynamic newValue)? onUpdateUser;

  const _AccountInfo({required this.user, required this.onUpdateUser});

  @override
  State<_AccountInfo> createState() => _AccountInfoState();
}

class _AccountInfoState extends State<_AccountInfo> {
  final double _widthImage = CSpace.width * 0.2;
  MUser? newUser;

  @override
  Widget build(BuildContext context) {
    return WForm<MUser>(
      list: _listFormItem,
      builder: (items) {
        return CustomScrollView(
          physics: const ClampingScrollPhysics(),
          slivers: [
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(CSpace.xl3),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(
                        onTap: () {
                          UDialog().showForm(
                              title: "Ảnh đại diện",
                              textButton: 'Lưu',
                              formItem: [
                                MFormItem(
                                    type: EFormItemType.upload,
                                    name: 'avatarUrl',
                                    label: 'Ảnh đại diện',
                                    uploadType: UploadType.single,
                                    prefix: 'user_avatar')
                              ],
                              api: (value, page, size, sort) async {
                                var result = await RepositoryProvider.of<Api>(context).auth.updateAvatar(
                                    body: {'avatarUrl': value['avatarUrl'][0]['file'], 'userId': widget.user.id});
                                if (result!.isSuccess) {
                                  setState(() {
                                    widget.user.avatarUrl = value['avatarUrl'][0]['file'];
                                  });
                                  context.pop();
                                }
                              });
                        },
                        child: Stack(
                          children: [
                            CircleAvatar(
                              radius: 50,
                              backgroundImage: widget.user.avatarUrl != "" ? NetworkImage(widget.user.avatarUrl) : null,
                              child: widget.user.avatarUrl == ""
                                  ?  widget.user.name.length > 3 ?  Text(
                                      widget.user.name.substring(0, 2),
                                      style: const TextStyle(color: Colors.white),
                                    ) : Text(
                                widget.user.name,
                                style: const TextStyle(color: Colors.white),
                              )
                                  : null,
                            ),
                            Positioned(
                                right: 3,
                                bottom: 6,
                                child: Container(
                                    height: _widthImage * 0.26,
                                    width: _widthImage * 0.26,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.white, width: 2),
                                      color: CColor.black.shade100,
                                      boxShadow: const [
                                        BoxShadow(color: Colors.grey, offset: Offset(0.5, 0.5), blurRadius: 1)
                                      ],
                                      shape: BoxShape.circle,
                                    ),
                                    child: FittedBox(child: Icon(Icons.camera_alt, color: CColor.blue))))
                          ],
                        )),
                    const VSpacer(CSpace.sm),
                    BlocBuilder<_EditBloc, bool>(builder: (context, isEdit) {
                      if (!isEdit) {
                        return _buildCommonInfo();
                      } else {
                        return const Center();
                      }
                    }),
                  ],
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: CSpace.xl5),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const VSpacer(CSpace.xl3),
                    BlocBuilder<_EditBloc, bool>(builder: (context, isEdit) {
                      if (isEdit) {
                        return editInfo(items);
                      } else {
                        return listInfo();
                      }
                    }),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  void initState() {
    context.read<BlocC<MUser>>().setStatus(status: AppStatus.init);

    _init();
    super.initState();
  }

  List<MFormItem> _listFormItem = [];

  Future<void> _init() async {
    newUser = widget.user.copyWith();
    widget.onUpdateUser?.call(newUser);

    List<MOption> opts = [
      MOption(value: 'male', label: 'Nam'),
      MOption(value: 'female', label: 'Nữ'),
      MOption(value: 'custom', label: 'Khác'),
    ];

    _listFormItem = [
      // BASIC INFO SECTION - tương tự iOS "THÔNG TIN CƠ BẢN"

      // Email (read-only) - tương tự iOS lbEmail
      MFormItem(
        name: 'email',
        label: 'Email',
        value: widget.user.email,
        isWantNewInput: false,
        keyBoard: EFormItemKeyBoard.email,
        enabled: false,
      ),

      // Full Name - tương tự iOS tfName
      MFormItem(
        name: 'Fullname',
        label: 'Họ và tên',
        value: widget.user.name,
        isWantNewInput: false,
        onValidator: (value, listController) {
          if (value.isEmpty) {
            return 'Vui lòng nhập họ và tên';
          }
          return null;
        },
      ),

      // Gender - tương tự iOS tfGender (moved up to match iOS order)
      MFormItem(
        name: 'gender',
        label: 'Giới tính',
        code: newUser?.genderString,
        value: newUser?.genderString,
        type: EFormItemType.select,
        items: opts,
        isWantNewInput: false,
        required: false,
        selectLabel: (MOption item) => item.label,
        selectValue: (MOption item) => item.value,
        format: (dynamic item) => item,
        itemSelect: (item, int index, selected) => Container(
          padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
          child: itemList(
            title: Text(
              item.label,
              style: TextStyle(fontSize: CFontSize.sm, color: selected ? Colors.white : Colors.black)
            ),
          )
        ),
      ),

      // Birth Date - tương tự iOS tfBirthdate
      MFormItem(
        name: 'birthdate',
        label: 'Ngày sinh',
        value: widget.user.birthdate,
        type: EFormItemType.date,
        isWantNewInput: false,
        required: false,
        onChange: (value, listController) {
          newUser?.birthdate = value;
          setState(() {
            widget.onUpdateUser?.call(newUser);
          });
        },
      ),

      // CONTACT INFO SECTION - tương tự iOS "THÔNG TIN LIÊN HỆ"

      // Personal ID/CMND - tương tự iOS cmndTextField (moved to contact section)
      MFormItem(
        name: 'personalId',
        label: 'CMND/CCCD',
        value: widget.user.personalId ?? '',
        isWantNewInput: false,
        required: false,
        keyBoard: EFormItemKeyBoard.number,
        onValidator: (value, listController) {
          if (value.isNotEmpty && value.length < 9) {
            return 'CMND/CCCD không hợp lệ';
          }
          return null;
        },
      ),

      // Phone Number - tương tự iOS tfPhoneNumber (moved to contact section)
      MFormItem(
        name: 'phoneOffice',
        label: 'Số điện thoại',
        value: widget.user.phoneNumber,
        keyBoard: EFormItemKeyBoard.phone,
        onValidator: (value, listController) {
          if (value.isEmpty) {
            return 'Vui lòng nhập số điện thoại';
          } else if ((value as String).length != 10 || !RegExp(r'^(?:[+0]9)?[0-9]{10,12}$').hasMatch(value)) {
            return 'Số điện thoại không hợp lệ';
          }
          return null;
        },
        isWantNewInput: false,
      ),
      // City - matches iOS tfCity and Android selectionCity
      MFormItem(
        name: 'city',
        label: 'Tỉnh/Thành phố',
        code: '${widget.user.addressCityId}|${widget.user.addressCity}',
        value: widget.user.addressCityId,
        required: false,
        isWantNewInput: false,
        suffix: const Icon(Icons.keyboard_arrow_down),
        type: EFormItemType.select,
        api: (filter, page, size, sort) => RepositoryProvider.of<Api>(context).city.getListCity(),
        selectLabel: (MCityModel item) => item.name,
        selectValue: (MCityModel item) => '${item.id}|${item.name}',
        format: (dynamic item) => MCityModel.fromJson(item),
        onChange: (value, listController) {
          // Clear district when city changes - matches iOS/Android behavior
          listController['district']?.clear();
          newUser?.addressDistrict = '';
          newUser?.addressCityId = value?.split('|')[0];
          newUser?.addressCity = value?.split('|')[1];
          setState(() {
            widget.onUpdateUser?.call(newUser);
          });
        },
        itemSelect: (item, index, selected) => Container(
            padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
            child: ListTile(
              title: Text(item.name),
              selected: selected,
            )),
      ),
      // District - matches iOS tfDistrict and Android selectionDistrict
      MFormItem(
        name: 'district',
        label: 'Quận/Huyện',
        value: widget.user.addressDistrict ?? '',
        isWantNewInput: false,
        required: false,
        suffix: const Icon(Icons.keyboard_arrow_down),
        type: EFormItemType.select,
        api: (filter, page, size, sort) {
          // Get cityId from current city selection
          String? cityId = newUser?.addressCityId;
          if (cityId == null || cityId.isEmpty) {
            return Future.value(null); // No city selected, can't load districts
          }
          return RepositoryProvider.of<Api>(context).city.getDistrictOfCity(cityId: cityId);
        },
        selectLabel: (MCityModel item) => item.name,
        selectValue: (MCityModel item) => item.name,
        format: (dynamic item) => MCityModel.fromJson(item),
        onChange: (value, listController) {
          newUser?.addressDistrict = value;
          setState(() {
            widget.onUpdateUser?.call(newUser);
          });
        },
        itemSelect: (item, index, selected) => Container(
            padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
            child: ListTile(
              title: Text(item.name),
              selected: selected,
            )),
      ),

      // Address - tương tự iOS tfAddress
      MFormItem(
        name: 'address',
        label: 'Địa chỉ',
        value: widget.user.addressStreet ?? '',
        isWantNewInput: false,
        required: false,
      ),
    ];
  }

  Widget _buildCommonInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: CSpace.sm),
          child: Text(widget.user.name,
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: CFontSize.xl2, color: CColor.primary)),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: CSpace.sm),
          child: widget.user.userName.length > 1
              ? Text(
                  widget.user.userName,
                  style: TextStyle(color: CColor.primary),
                )
              : const SizedBox.shrink(),
        )
      ],
    );
  }

  Widget image() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(_widthImage),
      child: InkWell(
        onTap: () {
          showModal();
        },
        child: Container(
          height: _widthImage,
          width: _widthImage,
          padding: widget.user.avatarUrl.isNotEmpty ? EdgeInsets.zero : const EdgeInsets.all(CSpace.xl3),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white, width: 2),
            color: CColor.primary,
            boxShadow: const [BoxShadow(color: Colors.grey, offset: Offset(0.5, 0.5), blurRadius: 1)],
            shape: BoxShape.circle,
          ),
          child: FittedBox(
            fit: BoxFit.cover,
            child: widget.user.avatarUrl.isNotEmpty
                ? Image.network(widget.user.avatarUrl)
                : Text(
                    widget.user.name.substring(0, 2),
                    style: const TextStyle(color: Colors.white),
                  ),
          ),
        ),
      ),
    );
  }

  void showModal() {
    FocusScope.of(context).unfocus();
    showModalBottomSheet<void>(
      context: context,
      builder: (_) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: CSpace.sm),
          child: Wrap(
            children: [
              listTile(
                  svg: 'assets/svgs/upload/image.svg',
                  name: 'widgets.form.upload.Select image from gallery'.tr(),
                  onTap: () {
                    _onImageButtonPressed(source: ImageSource.gallery);
                  }),
              listTile(
                  svg: 'assets/svgs/upload/camera.svg',
                  name: 'widgets.form.upload.Take a photo'.tr(),
                  onTap: () {
                    _onImageButtonPressed(source: ImageSource.camera);
                  }),
            ],
          ),
        );
      },
    );
  }

  void _onImageButtonPressed({required ImageSource source}) async {
    final XFile? pickedFile = await _picker.pickImage(source: source);
    if (pickedFile != null) {
      await addImage(imageFile: pickedFile);
    }
  }

  Future<void> addImage({
    required XFile imageFile,
  }) async {
    Navigator.of(context).pop();
    final MUpload? data = await RepositoryProvider.of<Api>(context).postUploadPhysicalBlob(
      file: imageFile,
      prefix: 'user-avatar',
      obj: {
        'docType': 'user-avatar',
      },
    );
    if (data != null) {
      final result = await RepositoryProvider.of<Api>(context)
          .user
          .updateImage(url: data.fileUrl, userId: context.read<AuthC>().state.user?.id);
      if (result?.isSuccess == true) {
        context.read<AuthC>().check(context: context);
      }
    }
  }

  Widget editInfo(Map<String, Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Basic Info Section - tương tự iOS "THÔNG TIN CƠ BẢN"
        _buildSectionTitle('Thông tin cơ bản'),
        items['email'] ?? const SizedBox.shrink(),
        items['Fullname'] ?? const SizedBox.shrink(),
        items['gender'] ?? const SizedBox.shrink(),
        items['birthdate'] ?? const SizedBox.shrink(),

        // Contact Info Section - tương tự iOS "THÔNG TIN LIÊN HỆ"
        const VSpacer(16),
        _buildSectionTitle('Thông tin liên hệ'),
        items['personalId'] ?? const SizedBox.shrink(),
        items['phoneOffice'] ?? const SizedBox.shrink(),
        items['city'] ?? const SizedBox.shrink(),
        items['district'] ?? const SizedBox.shrink(),
        items['address'] ?? const SizedBox.shrink(),
      ],
    );
  }

  Widget listInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Basic Info Section - tương tự iOS "THÔNG TIN CƠ BẢN"
        _buildSectionTitle('Thông tin cơ bản'),
        info(title: 'Email', content: widget.user.email ?? ''),
        const VSpacer(8),
        info(title: 'Họ và tên', content: widget.user.name),
        const VSpacer(8),
        info(title: 'Giới tính', content: widget.user.genderString ?? ''),
        const VSpacer(8),
        info(title: 'Ngày sinh', content: Convert.date(widget.user.birthdate ?? '')),

        // Contact Info Section - tương tự iOS "THÔNG TIN LIÊN HỆ"
        // Always show contact info section since phone number is required
        const VSpacer(16),
        _buildSectionTitle('Thông tin liên hệ'),
        if (widget.user.personalId != null && widget.user.personalId!.isNotEmpty) ...[
          info(title: 'CMND/CCCD', content: widget.user.personalId ?? ''),
          const VSpacer(8),
        ],
        info(title: 'Số điện thoại', content: widget.user.phoneNumber?.toString() ?? ''),
        if (widget.user.addressCity != null && widget.user.addressCity!.isNotEmpty) ...[
          const VSpacer(8),
          info(title: 'Tỉnh/Thành phố', content: widget.user.addressCity ?? ''),
        ],
        if (widget.user.addressDistrict != null && widget.user.addressDistrict!.isNotEmpty) ...[
          const VSpacer(8),
          info(title: 'Quận/Huyện', content: widget.user.addressDistrict ?? ''),
        ],
        if (widget.user.addressStreet != null && widget.user.addressStreet!.isNotEmpty) ...[
          const VSpacer(8),
          info(title: 'Địa chỉ', content: widget.user.addressStreet ?? ''),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: CColor.primary,
        ),
      ),
    );
  }



  Widget info({required String title, required String content}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: TextStyle(fontWeight: FontWeight.w500, color: CColor.black)),
        const VSpacer(1),
        Container(
            width: CSpace.width,
            decoration: BoxDecoration(
                color: CColor.black[0], border: const Border(bottom: BorderSide(color: Colors.black12, width: 1))),
            padding: const EdgeInsets.fromLTRB(0, 10, 10, 10),
            child: Text(content, style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold))),
        const VSpacer(12),
      ],
    );
  }

  final ImagePicker _picker = ImagePicker();
}
