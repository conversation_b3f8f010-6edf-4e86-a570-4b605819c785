# 🚀 Cách chạy Auto Tests cho màn đăng nhập

## 📋 Y<PERSON>u cầu trước khi chạy

1. **Flutter SDK** đã được cài đặt
2. **Device/Emulator** đang chạy (cho integration tests)
3. **Dependencies** đã được cài đặt:
   ```bash
   flutter pub get
   ```

## 🧪 Các loại test có sẵn

### 1. Demo Test (Khuyến nghị cho người mới)
File: `test/demo_login_test.dart`
- ✅ Test cơ bản nhất, d<PERSON> hiểu
- ✅ C<PERSON> comments chi tiết bằng tiếng Việt
- ✅ 8 test cases từ cơ bản đến nâng cao

### 2. Advanced Test
File: `test/login_advanced_test.dart`
- 🔥 Test nâng cao với mocking
- 🔥 Performance testing
- 🔥 Security testing

### 3. Widget Test
File: `test/widget/login_widget_test.dart`
- 🎨 Test UI components riêng lẻ
- 🎨 Nhanh hơn integration test

## ⚡ Cách chạy tests

### Chạy Demo Test (Khuyến nghị)
```bash
# Chạy demo test với output chi tiết
flutter test test/demo_login_test.dart --reporter=expanded

# Hoặc chạy trên device thật
flutter test test/demo_login_test.dart -d <device_id>
```

### Chạy tất cả tests
```bash
# Chạy tất cả unit và widget tests
flutter test

# Chạy với coverage
flutter test --coverage
```

### Chạy integration tests
```bash
# Chạy trên emulator/device
flutter test integration_test/

# Chạy test cụ thể
flutter test test/login_test.dart
```

## 📱 Chạy trên device thật

1. **Kết nối device** và enable USB debugging
2. **Kiểm tra device** đã được nhận diện:
   ```bash
   flutter devices
   ```
3. **Chạy test** trên device:
   ```bash
   flutter test test/demo_login_test.dart -d <device_id>
   ```

## 🔍 Xem kết quả test

### Console Output
```bash
# Xem output chi tiết
flutter test --reporter=expanded

# Xem output JSON
flutter test --reporter=json
```

### Test Coverage
```bash
# Generate coverage report
flutter test --coverage

# Xem coverage (cần cài lcov)
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

## 🐛 Troubleshooting

### Lỗi thường gặp:

#### 1. "No connected devices"
```bash
# Kiểm tra devices
flutter devices

# Khởi động emulator
flutter emulators --launch <emulator_id>
```

#### 2. "Test failed to run"
```bash
# Clean và rebuild
flutter clean
flutter pub get
flutter test
```

#### 3. "Widget not found"
```bash
# Thêm delay để đợi UI render
await tester.pumpAndSettle();
await Future.delayed(Duration(seconds: 1));
```

#### 4. "API timeout"
```bash
# Tăng timeout trong test
await pumpUntilFound(tester, finder, 
    timeout: Duration(seconds: 30));
```

## 📊 Hiểu kết quả test

### ✅ Test PASSED
```
✓ Test 1: Kiểm tra UI elements có hiển thị đúng không
✓ Test 2: Kiểm tra validation khi form trống
```

### ❌ Test FAILED
```
✗ Test 3: Kiểm tra validation email không hợp lệ
  Expected: findsOneWidget
  Actual: findsNothing
```

### ⏱️ Test Performance
```
📊 Login page loaded in 1250ms
📊 Form submitted in 300ms
```

## 🎯 Test Cases trong Demo

| Test | Mô tả | Kết quả mong đợi |
|------|-------|------------------|
| Test 1 | UI Elements | Tất cả elements hiển thị đúng |
| Test 2 | Form trống | Hiển thị validation errors |
| Test 3 | Email không hợp lệ | Hiển thị lỗi email format |
| Test 4 | Tài khoản không tồn tại | Hiển thị "không tồn tại" |
| Test 5 | Mật khẩu sai | Hiển thị "Sai mật khẩu" |
| Test 6 | Đăng nhập thành công | Điều hướng đến Home |
| Test 7 | Quên mật khẩu | Điều hướng đến forgot password |
| Test 8 | Đăng ký | Điều hướng đến register |

## 🔧 Tùy chỉnh tests

### Thay đổi test data
```dart
// Trong file test, sửa credentials
await tester.enterText(emailFields.first, '<EMAIL>');
await tester.enterText(emailFields.last, 'your-password');
```

### Thêm test case mới
```dart
testWidgets('Test case mới', (WidgetTester tester) async {
  // Arrange - Chuẩn bị
  await initAppWidgetTest(tester);
  
  // Act - Thực hiện hành động
  // ... your test actions
  
  // Assert - Kiểm tra kết quả
  expect(find.text('Expected result'), findsOneWidget);
});
```

## 📚 Tài liệu tham khảo

- [Flutter Testing Guide](./LOGIN_TESTING_GUIDE.md) - Hướng dẫn chi tiết
- [Flutter Official Testing Docs](https://docs.flutter.dev/testing)
- [Widget Testing](https://docs.flutter.dev/cookbook/testing/widget)
- [Integration Testing](https://docs.flutter.dev/cookbook/testing/integration)

## 💡 Tips

1. **Chạy demo test trước** để hiểu cách testing hoạt động
2. **Đọc console output** để hiểu test đang làm gì
3. **Chạy từng test riêng lẻ** khi debug
4. **Sử dụng breakpoints** trong IDE để debug
5. **Kiểm tra device/emulator** có đang hoạt động không

---

**Happy Testing!** 🧪✨

Nếu gặp vấn đề, hãy kiểm tra:
1. Flutter version: `flutter --version`
2. Device connection: `flutter devices`
3. Dependencies: `flutter pub deps`
