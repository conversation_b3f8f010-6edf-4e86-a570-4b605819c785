import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

import '/constants/index.dart';
import '/core/index.dart';
import '/cubit/index.dart';
import '/models/index.dart';
import '/utils/index.dart';

part '_account_info.dart';

part '_edit_bloc.dart';

class MyAccountInfo extends StatefulWidget {
  const MyAccountInfo({super.key});

  @override
  State<MyAccountInfo> createState() => _MyAccountInfoState();
}

class _MyAccountInfoState extends State<MyAccountInfo> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _EditBloc(),
      child: blocForm<MUser>(child: const _Render()),
      // child:const _Render(),
    );
  }

  @override
  void initState() {
    context.read<AuthC>().check(context: context);

    // print(context.read<BlocC<MUser>>().state.data.toJson());
    super.initState();
  }
}

class _Render extends StatefulWidget {
  const _Render({super.key});

  @override
  State<_Render> createState() => _RenderState();
}

class _RenderState extends State<_Render> {
  MUser? newUser;

  void updateUser(dynamic newValue) {
    newUser = newValue;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(title: 'Thông tin cá nhân',titleColor: Colors.white, actions: [
        // if(context.read<_EditBloc>().e)

        BlocBuilder<_EditBloc, bool>(builder: (context, isEdit) {
          return !isEdit ? InkWell(
            child: Icon(Icons.edit, color: CColor.blue),
            onTap: () {
              context.read<_EditBloc>().switchMode(true);
            },
          ) : const Center();
        }),
        const SizedBox(width: 20.0)
      ]),
      backgroundColor: Colors.white,
      body: BlocBuilder<AuthC, AuthS>(
        builder: (context, state) {
          return state.user != null && state.status == AppStatus.success
              ? _AccountInfo(user: state.user!, onUpdateUser: updateUser)
              : const Center(child: WLoading());
        },
      ),
      bottomNavigationBar: BlocBuilder<_EditBloc, bool>(
        builder: (context, isEdit) {
          if (isEdit) {
            return SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(CSpace.xl),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () {
                        UDialog().showConfirm(
                          title: 'Lưu thay đổi',
                          body: const Text(
                            'Bạn có muốn lưu thông tin thay đổi này không?',
                            style: TextStyle(fontSize: CFontSize.sm),
                            textAlign: TextAlign.center,
                          ),
                          btnOkOnPress: () {
                            context.read<BlocC<MUser>>().submit(
                                api: (value, _, __, ___) {
                                  // Get current user ID - matches iOS/Android pattern
                                  final currentUser = context.read<AuthC>().state.user;
                                  final userId = currentUser?.accountId ?? '';
                                          // print('xxxxx' + value.toString());
                                          // print('xxxxx' + currentUser!.toJson().toString());
                                  return RepositoryProvider.of<Api>(context).auth.updateProfile(
                                    userId: userId,  // ✅ Pass userId like iOS/Android
                                    body: value,
                                  );
                                },
                                onlyApi: true,
                                submit: (_) {
                                  context.pop();
                                  context.read<AuthC>().check(context: context);
                                  context.read<BlocC<MUser>>().resetValue(value: {});

                                  context.read<_EditBloc>().switchMode(false);
                                });
                          },
                        );
                      },
                      child: Container(
                          width: CSpace.width,
                          decoration: BoxDecoration(color: CColor.blue, borderRadius: BorderRadius.circular(10)),
                          padding: const EdgeInsets.all(12),
                          child: const Center(
                              child: Text(
                            'Lưu thay đổi',
                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                          ))),
                    ),
                    const VSpacer(10),
                    InkWell(
                      onTap: () {
                        context.read<_EditBloc>().switchMode(false);
                        context.read<BlocC<MUser>>().resetValue(value: {});
                      },
                      child: Container(
                          width: CSpace.width,
                          decoration: BoxDecoration(
                            color: CColor.black[0],
                            borderRadius: BorderRadius.circular(10),
                          ),
                          padding: const EdgeInsets.all(12),
                          child: const Center(
                              child: Text(
                            'Huỷ bỏ',
                            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                          ))),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return SafeArea(
                child: Padding(
              padding: const EdgeInsets.all(CSpace.xl),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () => context.pushNamed(CRoute.myAccountPass),
                    child: Container(
                        width: CSpace.width,
                        decoration: BoxDecoration(color: CColor.blue, borderRadius: BorderRadius.circular(10)),
                        padding: const EdgeInsets.all(12),
                        child: const Center(
                            child: Text(
                          'Đổi mật khẩu',
                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        ))),
                  ),
                  const VSpacer(10),
                  InkWell(
                    onTap: () {
                      UDialog().showConfirm(
                          title: 'Đăng xuất',
                          text: 'Phiên làm việc sẽ kết thúc. Bạn có chắc muốn đăng xuất?',
                          btnOkText: 'Có',
                          btnCancelText: 'Không',
                          btnCancelStyle: TextStyle(color: CColor.blue, fontWeight: FontWeight.bold),
                          btnOkOnPress: () async {
                            await UDialog().delay();
                            context.read<AuthC>().logout();
                            context.goNamed(CRoute.login);
                          });
                    },
                    child: Container(
                        width: CSpace.width,
                        decoration: BoxDecoration(
                          color: CColor.black[0],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.all(12),
                        child: const Center(
                            child: Text(
                          'Đăng xuất',
                          style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                        ))),
                  )
                ],
              ),
            ));
          }
          return const SizedBox();
        },
      ),
    );
  }
}
