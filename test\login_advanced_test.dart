import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../lib/main.dart';
import '../lib/cubit/index.dart';
import '../lib/service/index.dart';
import '../lib/models/index.dart';
import 'common.dart';

// Generate mocks for testing
@GenerateMocks([http.Client, Api])
class MockApi extends Mock implements Api {}

/// Advanced Login Testing Suite
/// 
/// Bộ test nâng cao cho màn đăng nhập bao gồm:
/// 1. Unit Tests - Test logic riêng lẻ
/// 2. Widget Tests - Test UI components
/// 3. Integration Tests - Test toàn bộ flow
/// 4. Mock API Tests - Test với dữ liệu giả
/// 5. Error Handling Tests - Test xử lý lỗi
/// 6. Performance Tests - Test hiệu suất
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('🧪 Advanced Login Tests', () {
    late MockApi mockApi;

    setUp(() {
      mockApi = MockApi();
      // Setup SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    group('📱 Widget Tests - Test UI Components', () {
      testWidgets('Login Form UI Elements Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        
        // Navigate to login page
        await navigateToLogin(tester);

        // Test all form elements exist
        expect(find.byType(TextFormField), findsNWidgets(2)); // Email + Password
        expect(find.byType(ElevatedButton), findsAtLeastNWidgets(1)); // Login button
        expect(find.byType(Checkbox), findsOneWidget); // Remember me
        expect(find.text('Quên mật khẩu?'), findsOneWidget);
        expect(find.text('Đăng ký'), findsOneWidget);
      });

      testWidgets('Password Visibility Toggle Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        // Find password field
        final passwordField = find.byKey(const ValueKey("Mật khẩu"));
        expect(passwordField, findsOneWidget);

        // Test password is initially hidden
        final passwordWidget = tester.widget<TextFormField>(passwordField);
        expect(passwordWidget.obscureText, isTrue);

        // Find and tap visibility toggle (if exists)
        final visibilityToggle = find.byIcon(Icons.visibility);
        if (visibilityToggle.evaluate().isNotEmpty) {
          await tester.tap(visibilityToggle);
          await tester.pumpAndSettle();
          
          // Verify password is now visible
          final updatedPasswordWidget = tester.widget<TextFormField>(passwordField);
          expect(updatedPasswordWidget.obscureText, isFalse);
        }
      });

      testWidgets('Form Validation Messages Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        // Test empty form submission
        await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
        await tester.pumpAndSettle();

        // Should show validation errors
        expect(find.textContaining('email'), findsAtLeastNWidgets(1));
        expect(find.textContaining('mật khẩu'), findsAtLeastNWidgets(1));
      });
    });

    group('🔧 Unit Tests - Test Business Logic', () {
      test('Email Validation Logic Test', () {
        // Test valid emails
        expect(isValidEmail('<EMAIL>'), isTrue);
        expect(isValidEmail('<EMAIL>'), isTrue);
        expect(isValidEmail('<EMAIL>'), isTrue);

        // Test invalid emails
        expect(isValidEmail('invalid-email'), isFalse);
        expect(isValidEmail('test@'), isFalse);
        expect(isValidEmail('@example.com'), isFalse);
        expect(isValidEmail(''), isFalse);
      });

      test('Password Validation Logic Test', () {
        // Test valid passwords
        expect(isValidPassword('123456'), isTrue);
        expect(isValidPassword('password123'), isTrue);
        expect(isValidPassword('MySecurePass!'), isTrue);

        // Test invalid passwords
        expect(isValidPassword('12345'), isFalse); // Too short
        expect(isValidPassword(''), isFalse); // Empty
        expect(isValidPassword('   '), isFalse); // Only spaces
      });

      test('Login Request Model Test', () {
        final loginData = {
          'username': '<EMAIL>',
          'password': 'password123',
          'deviceId': 'test-device-id',
          'rememberMe': true,
        };

        expect(loginData['username'], equals('<EMAIL>'));
        expect(loginData['password'], equals('password123'));
        expect(loginData['rememberMe'], isTrue);
      });
    });

    group('🌐 API Integration Tests', () {
      testWidgets('Successful Login API Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        // Enter valid test credentials
        await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
        await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

        // Hide keyboard and submit
        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle();
        
        await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
        await tester.pumpAndSettle();

        // Wait for API response and navigation
        await pumpUntilFound(tester, find.text("Home"), timeout: const Duration(seconds: 10));
        expect(find.text("Home"), findsOneWidget);
      });

      testWidgets('Failed Login API Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        // Enter invalid credentials
        await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
        await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), 'wrongpassword');

        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle();
        
        await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
        await tester.pumpAndSettle();

        // Should show error message
        await pumpUntilFound(tester, find.textContaining("không tồn tại"), timeout: const Duration(seconds: 10));
        expect(find.textContaining("không tồn tại"), findsOneWidget);
      });
    });

    group('🔒 Security Tests', () {
      testWidgets('Captcha Integration Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        // Enter credentials
        await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
        await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), 'password123');

        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle();
        
        // Submit form - should trigger captcha in production
        await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
        await tester.pumpAndSettle();

        // In development mode, captcha returns dummy token
        // In production, would show captcha dialog
        expect(find.text("Captcha validation failed"), findsNothing);
      });

      testWidgets('Remember Me Functionality Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        // Find remember me checkbox
        final rememberMeText = find.text("Ghi nhớ tài khoản");
        expect(rememberMeText, findsOneWidget);

        // Tap to enable remember me
        await tester.tap(rememberMeText);
        await tester.pumpAndSettle();

        // Enter credentials and login
        await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
        await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle();
        
        await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
        await tester.pumpAndSettle();

        // Should save credentials for next time
        // This would require checking SharedPreferences in a real test
      });
    });

    group('📱 Social Login Tests', () {
      testWidgets('Facebook Login Button Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        final facebookButton = find.text("Đăng nhập với Facebook");
        expect(facebookButton, findsOneWidget);

        // Test button is tappable
        await tester.tap(facebookButton);
        await tester.pumpAndSettle();

        // Note: Actual Facebook login would require platform channel mocking
      });

      testWidgets('Apple Login Button Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        final appleButton = find.text("Đăng nhập với Apple");
        expect(appleButton, findsOneWidget);

        // Test button is tappable
        await tester.tap(appleButton);
        await tester.pumpAndSettle();

        // Note: Actual Apple login would require platform channel mocking
      });
    });

    group('🚀 Performance Tests', () {
      testWidgets('Login Page Load Performance Test', (WidgetTester tester) async {
        final stopwatch = Stopwatch()..start();
        
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);
        
        stopwatch.stop();
        
        // Login page should load within 2 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
        print('📊 Login page loaded in ${stopwatch.elapsedMilliseconds}ms');
      });

      testWidgets('Form Submission Performance Test', (WidgetTester tester) async {
        await initAppWidgetTest(tester);
        await navigateToLogin(tester);

        await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
        await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), 'password123');

        final stopwatch = Stopwatch()..start();
        
        await SystemChannels.textInput.invokeMethod('TextInput.hide');
        await tester.pumpAndSettle();
        await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
        await tester.pumpAndSettle();
        
        stopwatch.stop();
        
        // Form submission should be responsive (< 500ms for UI feedback)
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
        print('📊 Form submitted in ${stopwatch.elapsedMilliseconds}ms');
      });
    });
  });
}

// Helper functions for testing
Future<void> navigateToLogin(WidgetTester tester) async {
  await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
  await tapButtonPump(tester, 'Tiếp tục', type: TextButton);
  await tapButtonPump(tester, 'Bắt đầu', type: TextButton);
}

bool isValidEmail(String email) {
  return RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email);
}

bool isValidPassword(String password) {
  return password.trim().length >= 6;
}
