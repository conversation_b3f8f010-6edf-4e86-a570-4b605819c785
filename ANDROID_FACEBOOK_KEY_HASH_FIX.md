# 🔧 Android Facebook Key Hash Fix

## 🚨 **Vấn đề**

- ✅ **iOS**: Facebook login hoạt động bình thường (không cần key hash)
- ❌ **Android**: Lỗi "Invalid key hash" khi login Facebook

## 📊 **Phân tích cấu hình**

### **✅ Cấu hình đã đúng:**

| Setting | Flutter | iOS | Android | Status |
|---------|---------|-----|---------|---------|
| **Facebook App ID** | `***************` | `***************` | `***************` | ✅ MATCH |
| **Client Token** | `190092c46827b81fb057777a52a92f4b` | `190092c46827b81fb057777a52a92f4b` | N/A | ✅ MATCH |
| **Bundle/Package** | `com.beta.betacineplex` | `com.beta.betacineplex` | `vn.zenity.betacineplex` | ⚠️ KHÁC |
| **URL Scheme** | `fb***************` | `fb***************` | N/A | ✅ MATCH |

### **❌ Vấn đề chính:**

1. **Key Hash Missing**: Android cần key hash trong Facebook Developer Console
2. **Package Name**: Flutter sử dụng `com.beta.betacineplex` nhưng Android repo sử dụng `vn.zenity.betacineplex`

## 🛠️ **Solution: Generate Key Hash**

### **Method 1: Sử dụng Terminal (Recommended)**

```bash
# Navigate to project directory
cd /path/to/your/flutter/project

# Generate debug key hash
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android | openssl sha1 -binary | openssl base64

# Output sẽ là key hash, ví dụ:
# M/51abc123def456ghi789jkl...
```

### **Method 2: Sử dụng Android Studio**

```bash
# Open terminal trong Android Studio
cd android

# Run signing report
./gradlew signingReport

# Tìm SHA1 fingerprint trong output:
# SHA1: 12:34:56:78:90:AB:CD:EF...

# Convert SHA1 to Facebook key hash (sử dụng online converter hoặc script)
```

### **Method 3: Programmatic (Temporary)**

Thêm code này vào `MainActivity.kt` để print key hash:

```kotlin
// Thêm vào MainActivity.kt
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.util.Base64
import android.util.Log
import java.security.MessageDigest

// Thêm method này vào MainActivity class
private fun printKeyHash() {
    try {
        val info = packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNATURES)
        for (signature: Signature in info.signatures) {
            val md = MessageDigest.getInstance("SHA")
            md.update(signature.toByteArray())
            val keyHash = Base64.encodeToString(md.digest(), Base64.DEFAULT)
            Log.d("KeyHash", "🔑 Facebook Key Hash: $keyHash")
            println("🔑 Facebook Key Hash: $keyHash")
        }
    } catch (e: Exception) {
        Log.e("KeyHash", "Error: ${e.message}")
    }
}

// Gọi trong onCreate()
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    printKeyHash() // Thêm dòng này
    // ... rest of code
}
```

## 🔧 **Update Facebook Developer Console**

### **Step 1: Truy cập Facebook Developer Console**

1. Đi đến https://developers.facebook.com/
2. Login với Facebook account
3. Chọn app: `***************` (Betacineplex)

### **Step 2: Add Key Hash**

1. **Settings** → **Basic**
2. Scroll xuống **"Android"** section
3. Trong **"Key Hashes"** field:
   - Paste key hash vừa generate
   - Click **"Save Changes"**

### **Step 3: Verify Package Name**

Đảm bảo **"Package Name"** trong Facebook Console là: `com.beta.betacineplex`

## 📱 **Test Facebook Login**

### **Before Fix:**
```
❌ Invalid key hash. The key hash M/51... does not match any stored key hashes
```

### **After Fix:**
```
✅ Facebook login successful
✅ User data retrieved: {name: John Doe, email: <EMAIL>}
```

## 🧪 **Testing Steps**

### **1. Generate Key Hash:**

```bash
# Method 1: Terminal
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android | openssl sha1 -binary | openssl base64

# Method 2: Add to MainActivity.kt và run app
flutter run --debug
# Check logs for: 🔑 Facebook Key Hash: [HASH]
```

### **2. Update Facebook Console:**

1. Copy key hash
2. Paste vào Facebook Developer Console
3. Save changes

### **3. Test Login:**

```dart
// Test Facebook login
try {
  final result = await FacebookAuth.instance.login(
    permissions: ['public_profile', 'email']
  );
  
  if (result.status == LoginStatus.success) {
    print('✅ Facebook login successful!');
    final userData = await FacebookAuth.instance.getUserData();
    print('User data: $userData');
  }
} catch (e) {
  print('❌ Facebook login error: $e');
}
```

## 🔍 **Troubleshooting**

### **Issue 1: Key Hash vẫn invalid**

**Solution:** Generate multiple key hashes:

```bash
# Debug keystore
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android | openssl sha1 -binary | openssl base64

# Release keystore (nếu có)
keytool -exportcert -alias your_release_alias -keystore path/to/release.keystore | openssl sha1 -binary | openssl base64
```

Add tất cả key hashes vào Facebook Console.

### **Issue 2: Package name mismatch**

**Current Flutter:** `com.beta.betacineplex`  
**Android Repo:** `vn.zenity.betacineplex`

Đảm bảo Facebook Console sử dụng đúng package name: `com.beta.betacineplex`

### **Issue 3: Facebook App không tìm thấy**

Verify Facebook App ID trong:

```xml
<!-- android/app/src/main/res/values/strings.xml -->
<string name="facebook_app_id">***************</string>

<!-- ios/Runner/Info.plist -->
<key>FacebookAppID</key>
<string>***************</string>
```

## 📋 **Quick Fix Checklist**

- [ ] ✅ Generate key hash using keytool
- [ ] ✅ Copy key hash từ terminal output
- [ ] ✅ Login Facebook Developer Console
- [ ] ✅ Navigate to app `***************`
- [ ] ✅ Settings → Basic → Android
- [ ] ✅ Paste key hash vào "Key Hashes"
- [ ] ✅ Verify package name: `com.beta.betacineplex`
- [ ] ✅ Save changes
- [ ] ✅ Test Facebook login trong app

## 🎯 **Expected Result**

```
✅ iOS: Facebook login works (no changes needed)
✅ Android: Facebook login works after key hash fix
✅ No "Invalid key hash" error
✅ User data retrieved successfully
```

## 💡 **Why iOS works but Android doesn't?**

- **iOS**: Sử dụng Bundle ID để identify app, không cần key hash
- **Android**: Cần cả package name VÀ key hash để verify app authenticity
- **Facebook**: Requires key hash cho Android để prevent app spoofing

**Fix này sẽ giải quyết hoàn toàn vấn đề Facebook login trên Android!** 🎉
