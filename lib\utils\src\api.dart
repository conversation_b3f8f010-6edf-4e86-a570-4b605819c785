import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_app/service/project/cinema.dart';
import 'package:flutter_app/service/project/don_hang.dart';
import 'package:flutter_app/service/project/san_pham.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../service/language_service.dart';
import '../../service/project/film.dart';
import '../../service/project/other.dart';
import '/constants/index.dart';
import '/cubit/index.dart';
import '/models/index.dart';
import '/service/index.dart';
import '/service/src/city.dart';
import '/utils/index.dart';

class Api {
  SAuth get auth => SAuth(endpoint1, headers, checkAuth);

  SNavigation get navigation => SNavigation(endpoint, headers, checkAuth);

  SAddress get address => SAddress(endpoint, headers, checkAuth);

  SCity get city => SCity(endpoint1, headers, checkAuth);

  SUser get user => SUser(endpoint, headers, checkAuth);

  SCodeType get codetype => SCodeType(endpoint, headers, checkAuth);

  SDonHang get donhang => SDonHang(endpoint, headers, checkAuth);

  SSanPham get sanpham => SSanPham(endpoint, headers, checkAuth);

  SNotification get notification => SNotification(endpoint1, headers, checkAuth);

  SPhuongTien get phuongtien => SPhuongTien(endpoint, headers, checkAuth);

  STaiXe get taixe => STaiXe(endpoint, headers, checkAuth);

  SProductConfiguration get cauhinh => SProductConfiguration(endpoint, headers, checkAuth);

  SDeliveryNote get deliveryNote => SDeliveryNote(endpoint, headers, checkAuth);

  SHistory get lichsu => SHistory(endpoint, headers, checkAuth);

  SKho get kho => SKho(endpoint, headers, checkAuth);

  ///Beta
  SFilm get film => SFilm(endpoint1, headers, checkAuth);

  SCinema get cinema => SCinema(endpoint1, headers, checkAuth);

  SPromotion get promotion => SPromotion(endpoint1, headers, checkAuth);

  SOther get other => SOther(endpoint1, headers, checkAuth);

  final String endpoint = Environment.apiUrl;
  final String endpoint1 = 'http://dev.api.betacorp.vn';
  // final String endpoint1 = 'https://api.betacorp.vn'; // Production
  // Headers matching iOS Global.shared.headesrs() and Android APIClient exactly
  final Map<String, String> headers = {
    "channel": "mobile",                                    // ✅ Match iOS/Android
    "Content-Type": "application/json",                     // ✅ Match iOS (without charset)
    "device-type": Platform.isAndroid ? "android" : "ios", // ✅ Match iOS/Android
    "language": "vi",                                       // ✅ Match iOS/Android (vi/en)
    "sandbox_mode": "0"                                     // ✅ Match iOS/Android
  };

  Future<MApi?> checkAuth({required http.Response result, int? returnWithStatusCode, List? arrayMore, bool isThongBao = false}) async {
    if (result.statusCode == 504) {
      UDialog().showError(text: 'Không thể kết nối tới máy chủ');
      return null;
    }
    if (result.body.isEmpty) return null;
    if (result.statusCode == 401) {
      rootNavigatorKey.currentState!.context.read<AuthC>().error();
      return null;
    }
    final contentType = result.headers['Content-type'];
    if ((contentType != null && contentType.contains('text/html')) ||
        result.body.trimLeft().startsWith('<!DOCTYPE') ||
        result.body.trimLeft().startsWith('<html')) {
// Trả về MApi với kiểu dữ liệu raw HTML
      return MApi(
        code: result.statusCode,
        isSuccess: true,
        message: 'HTML content',
        data: result.body,
      );
    }
    if (contentType != null && contentType.contains('application/pdf')) {
      return MApi(
        code: result.statusCode,
        isSuccess: true,
        message: 'File data returned successfully',
        data: result.bodyBytes, // Attach the file data
      );
    }
    final body = jsonDecode(result.body);
    if (arrayMore != null && result.body is! List) {
      body['data']['content'] = [...body['data']['content'], ...arrayMore];
    }
    MApi response = MApi.fromJson(body);
    if (returnWithStatusCode != null && response.code == returnWithStatusCode) {
      return response;
    }
    if (response.code != 1 && response.message != null && !isThongBao) {
      Timer(const Duration(milliseconds: 50), () => UDialog().showError(text: response.message));
      return null;
    }
    if (response.data is List) {
      return response.copyWith(data: {
        'page': 1,
        'totalPages': response.data.length,
        'size': response.data.length,
        'numberOfElements': response.data.length,
        'totalElements': response.data.length,
        'content': response.data
      });
    }
    return response;
  }

  Future setToken({required String token}) async {
    headers['Authorization'] = 'Bearer $token';
  }

  Future setXUser({required String id}) async {
    headers['X-User'] = id;
  }

  Future clearHeader({required String name}) async {
    headers.remove(name);
  }

  Future setLanguage({required String language}) async {
    headers['x-language'] = language;
    // headers['language'] = language; // Add language header for API calls
  }

  /// Update the language header based on the current app language
  Future updateLanguageHeader() async {
    final languageService = LanguageService();
    final language = await languageService.getCurrentLanguage();
    await setLanguage(language: language);
  }

  Future<MUpload?> postUploadPhysicalBlob(
      {XFile? file, required String prefix, required Map<String, dynamic> obj, ByteData? byteData}) async {
    http.MultipartRequest request =
        http.MultipartRequest('POST', Uri.parse('$endpoint${Environment.uploadUrl}$prefix'));
    AppConsole.dump(request.url.queryParameters);
    request.headers['Content-Type'] = 'multipart/form-data';
    request.headers['Authorization'] = headers['Authorization'] ?? '';

    // http.ByteStream stream = http.ByteStream(file.openRead());
    // int length = await file.length();
    http.ByteStream stream;
    int length;

    if (byteData != null) {
      stream = http.ByteStream.fromBytes(byteData.buffer.asUint8List());
      length = byteData.lengthInBytes;
    } else if (file != null) {
      stream = http.ByteStream(file.openRead());
      length = await file.length();
    } else {
      throw Exception("Cần phải truyền vào một trong hai tham số: ByteData hoặc XFile.");
    }

    http.MultipartFile multipartFile = http.MultipartFile('file', stream, length,
        filename: byteData == null ? file?.name : 'file_${DateTime.now().millisecondsSinceEpoch}.png');
    request.files.add(multipartFile);

    // http.MultipartFile multipartFile = http.MultipartFile('file', stream, length, filename: file.name);
    // request.files.add(multipartFile);
    var response = await request.send();
    final res = await http.Response.fromStream(response).timeout(const Duration(seconds: 20), onTimeout: () {
      return http.Response('Error', 408);
    });
    if (res.statusCode == 413) UDialog().showError(text: 'Dung lượng tệp được tải lên quá lớn');
    if (res.statusCode > 300) return null;
    dynamic data = {...jsonDecode(res.body)['data'], ...obj};
    return MUpload.fromJson(data);
  }

  Future<List<MUpload>> getAttachmentsTemplate({String entityType = 'post'}) async {
    http.Response result = await BaseHttp.get(
        url: '$endpoint/upload/$entityType/attachment-templates', headers: headers, queryParameters: {});
    List<MUpload> data = [];
    if (result.statusCode < 400) {
      List body = jsonDecode(result.body)['data'];
      for (int i = 0; i < body.length; i++) {
        data.add(MUpload.fromJson(body[i]));
      }
    }
    return data;
  }

  Future<dynamic> downloadFile({required http.Response response, String nameFile = 'test.png'}) async {
    File file;
    String filePath = '';
    String dir = '';

    final String fileName = response.headers['content-disposition'] != null
        ? response.headers['content-disposition']!.split(';')[1].split('=')[1].replaceAll(r'/\"/g', '')
        : nameFile;
    if (Platform.isAndroid) {
      if (await Permission.storage.request().isGranted) {
        dir = (await getExternalStorageDirectory())!.path;
        dir = '$dir/Download';
        dir = dir.replaceAll('Android/data/${Environment.appPackage}/files/', '');
        await Directory(dir).create(recursive: true);
      }
    } else if (Platform.isIOS) {
      dir = (await getApplicationDocumentsDirectory()).path;
    }
    filePath = '$dir/$fileName';
    file = File(filePath);
    file.writeAsBytesSync(response.bodyBytes);
    return fileName;
  }
}
