import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// 🎨 Simple Widget Test cho Login Components
///
/// File này demo cách test các UI components đơn giản
/// mà không cần phức tạp với BLoC và dependencies
void main() {
  group('🎨 Simple Login Widget Tests', () {

    testWidgets('✅ Email field should accept text input', (WidgetTester tester) async {
      // Arrange - Tạo email field đơn giản
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextFormField(
              key: const Key('email_field'),
              decoration: const InputDecoration(
                labelText: 'Email',
                hintText: 'Nhập email của bạn',
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ),
        ),
      );

      // Act - Nhập text vào field
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.pumpAndSettle();

      // Assert - Kiểm tra text đã được nhập
      expect(find.text('<EMAIL>'), findsOneWidget);
      print('✅ Email field accepts text input correctly');
    });

    testWidgets('🔒 Password field should hide text', (WidgetTester tester) async {
      // Arrange - Tạo password field
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextFormField(
              key: const Key('password_field'),
              decoration: const InputDecoration(
                labelText: 'Password',
                hintText: 'Nhập mật khẩu',
              ),
              obscureText: true, // Ẩn text
            ),
          ),
        ),
      );

      // Act - Nhập password
      await tester.enterText(find.byKey(const Key('password_field')), 'secret123');
      await tester.pumpAndSettle();

      // Assert - Text bị ẩn, không hiển thị trực tiếp
      final passwordField = tester.widget<TextFormField>(find.byKey(const Key('password_field')));
      // expect(passwordField., isTrue);
      print('✅ Password field hides text correctly');
    });

    testWidgets('🔘 Login button should be tappable', (WidgetTester tester) async {
      // Arrange - Tạo login button với callback
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ElevatedButton(
              key: const Key('login_button'),
              onPressed: () {
                buttonPressed = true;
              },
              child: const Text('Đăng nhập'),
            ),
          ),
        ),
      );

      // Act - Tap button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();

      // Assert - Callback được gọi
      expect(buttonPressed, isTrue);
      print('✅ Login button is tappable and triggers callback');
    });

    testWidgets('☑️ Checkbox should toggle state', (WidgetTester tester) async {
      // Arrange - Tạo checkbox với state
      bool isChecked = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Checkbox(
                  key: const Key('remember_checkbox'),
                  value: isChecked,
                  onChanged: (value) {
                    setState(() {
                      isChecked = value ?? false;
                    });
                  },
                );
              },
            ),
          ),
        ),
      );

      // Assert initial state
      Checkbox checkbox = tester.widget(find.byKey(const Key('remember_checkbox')));
      expect(checkbox.value, isFalse);

      // Act - Tap checkbox
      await tester.tap(find.byKey(const Key('remember_checkbox')));
      await tester.pumpAndSettle();

      // Assert - State changed
      checkbox = tester.widget(find.byKey(const Key('remember_checkbox')));
      expect(checkbox.value, isTrue);
      print('✅ Checkbox toggles state correctly');
    });

    testWidgets('📱 Complete login form layout', (WidgetTester tester) async {
      // Arrange - Tạo complete login form
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: const Text('Đăng nhập')),
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Email field
                  TextFormField(
                    key: const Key('email_field'),
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      prefixIcon: Icon(Icons.email),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Password field
                  TextFormField(
                    key: const Key('password_field'),
                    decoration: const InputDecoration(
                      labelText: 'Mật khẩu',
                      prefixIcon: Icon(Icons.lock),
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),

                  // Remember me checkbox
                  Row(
                    children: [
                      Checkbox(
                        key: const Key('remember_checkbox'),
                        value: false,
                        onChanged: (value) {},
                      ),
                      const Text('Ghi nhớ tài khoản'),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Login button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      key: const Key('login_button'),
                      onPressed: () {},
                      child: const Text('Đăng nhập'),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Forgot password link
                  TextButton(
                    key: const Key('forgot_password_button'),
                    onPressed: () {},
                    child: const Text('Quên mật khẩu?'),
                  ),

                  // Social login buttons
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    key: const Key('facebook_button'),
                    onPressed: () {},
                    icon: const Icon(Icons.facebook),
                    label: const Text('Đăng nhập với Facebook'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    key: const Key('apple_button'),
                    onPressed: () {},
                    icon: const Icon(Icons.apple),
                    label: const Text('Đăng nhập với Apple'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Assert - Kiểm tra tất cả elements có mặt
      expect(find.text('Đăng nhập'), findsNWidgets(2)); // Title + Button
      expect(find.byKey(const Key('email_field')), findsOneWidget);
      expect(find.byKey(const Key('password_field')), findsOneWidget);
      expect(find.byKey(const Key('remember_checkbox')), findsOneWidget);
      expect(find.byKey(const Key('login_button')), findsOneWidget);
      expect(find.byKey(const Key('forgot_password_button')), findsOneWidget);
      expect(find.byKey(const Key('facebook_button')), findsOneWidget);
      expect(find.byKey(const Key('apple_button')), findsOneWidget);
      expect(find.text('Ghi nhớ tài khoản'), findsOneWidget);
      expect(find.text('Quên mật khẩu?'), findsOneWidget);

      print('✅ Complete login form layout renders correctly');
    });

    testWidgets('🔍 Form validation simulation', (WidgetTester tester) async {
      // Arrange - Tạo form với validation
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: Column(
                children: [
                  TextFormField(
                    key: const Key('email_field'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Email không được để trống';
                      }
                      if (!value.contains('@')) {
                        return 'Email không hợp lệ';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    key: const Key('password_field'),
                    validator: (value) {
                      if (value == null || value.length < 6) {
                        return 'Mật khẩu phải có ít nhất 6 ký tự';
                      }
                      return null;
                    },
                  ),
                  ElevatedButton(
                    onPressed: () {
                      formKey.currentState?.validate();
                    },
                    child: const Text('Validate'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Act - Submit form trống
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();

      // Assert - Validation errors hiển thị
      expect(find.text('Email không được để trống'), findsOneWidget);
      expect(find.text('Mật khẩu phải có ít nhất 6 ký tự'), findsOneWidget);

      // Act - Nhập email không hợp lệ
      await tester.enterText(find.byKey(const Key('email_field')), 'invalid-email');
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();

      // Assert - Email validation error
      expect(find.text('Email không hợp lệ'), findsOneWidget);

      print('✅ Form validation works correctly');
    });
  });
}
