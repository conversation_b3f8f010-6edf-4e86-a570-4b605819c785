import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// 🎨 Simple Widget Test cho Login Components
///
/// File này demo cách test các UI components đơn giản
/// mà không cần phức tạp với BLoC và dependencies
void main() {
  group('🎨 Simple Login Widget Tests', () {

    testWidgets('✅ Email field should accept text input', (WidgetTester tester) async {
      // Arrange - Tạo email field đơn giản
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextFormField(
              key: const Key('email_field'),
              decoration: const InputDecoration(
                labelText: 'Email',
                hintText: 'Nhập email của bạn',
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ),
        ),
      );

      // Act - Nhập text vào field
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.pumpAndSettle();

      // Assert - Kiểm tra text đã được nhập
      expect(find.text('<EMAIL>'), findsOneWidget);
      print('✅ Email field accepts text input correctly');
    });

    testWidgets('🔒 Password field should hide text', (WidgetTester tester) async {
      // Arrange - Tạo password field
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextFormField(
              key: const Key('password_field'),
              decoration: const InputDecoration(
                labelText: 'Password',
                hintText: 'Nhập mật khẩu',
              ),
              obscureText: true, // Ẩn text
            ),
          ),
        ),
      );

      // Act - Nhập password
      await tester.enterText(find.byKey(const Key('password_field')), 'secret123');
      await tester.pumpAndSettle();

      // Assert - Text bị ẩn, không hiển thị trực tiếp
      final passwordField = tester.widget<TextFormField>(find.byKey(const Key('password_field')));
      // expect(passwordField., isTrue);
      print('✅ Password field hides text correctly');
    });

    testWidgets('🔘 Login button should be tappable', (WidgetTester tester) async {
      // Arrange - Tạo login button với callback
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ElevatedButton(
              key: const Key('login_button'),
              onPressed: () {
                buttonPressed = true;
              },
              child: const Text('Đăng nhập'),
            ),
          ),
        ),
      );

      // Act - Tap button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();

      // Assert - Callback được gọi
      expect(buttonPressed, isTrue);
      print('✅ Login button is tappable and triggers callback');
    });

    testWidgets('☑️ Checkbox should toggle state', (WidgetTester tester) async {
      // Arrange - Tạo checkbox với state
      bool isChecked = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Checkbox(
                  key: const Key('remember_checkbox'),
                  value: isChecked,
                  onChanged: (value) {
                    setState(() {
                      isChecked = value ?? false;
                    });
                  },
                );
              },
            ),
          ),
        ),
      );

      // Assert initial state
      Checkbox checkbox = tester.widget(find.byKey(const Key('remember_checkbox')));
      expect(checkbox.value, isFalse);

      // Act - Tap checkbox
      await tester.tap(find.byKey(const Key('remember_checkbox')));
      await tester.pumpAndSettle();

      // Assert - State changed
      checkbox = tester.widget(find.byKey(const Key('remember_checkbox')));
      expect(checkbox.value, isTrue);
      print('✅ Checkbox toggles state correctly');
    });

    testWidgets('📱 Complete login form layout', (WidgetTester tester) async {
      // Arrange - Tạo complete login form
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: const Text('Đăng nhập')),
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Email field
                  TextFormField(
                    key: const Key('email_field'),
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      prefixIcon: Icon(Icons.email),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Password field
                  TextFormField(
                    key: const Key('password_field'),
                    decoration: const InputDecoration(
                      labelText: 'Mật khẩu',
                      prefixIcon: Icon(Icons.lock),
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),

                  // Remember me checkbox
                  Row(
                    children: [
                      Checkbox(
                        key: const Key('remember_checkbox'),
                        value: false,
                        onChanged: (value) {},
                      ),
                      const Text('Ghi nhớ tài khoản'),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Login button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      key: const Key('login_button'),
                      onPressed: () {},
                      child: const Text('Đăng nhập'),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Forgot password link
                  TextButton(
                    key: const Key('forgot_password_button'),
                    onPressed: () {},
                    child: const Text('Quên mật khẩu?'),
                  ),

                  // Social login buttons
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    key: const Key('facebook_button'),
                    onPressed: () {},
                    icon: const Icon(Icons.facebook),
                    label: const Text('Đăng nhập với Facebook'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    key: const Key('apple_button'),
                    onPressed: () {},
                    icon: const Icon(Icons.apple),
                    label: const Text('Đăng nhập với Apple'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Assert - Kiểm tra tất cả elements có mặt
      expect(find.text('Đăng nhập'), findsNWidgets(2)); // Title + Button
      expect(find.byKey(const Key('email_field')), findsOneWidget);
      expect(find.byKey(const Key('password_field')), findsOneWidget);
      expect(find.byKey(const Key('remember_checkbox')), findsOneWidget);
      expect(find.byKey(const Key('login_button')), findsOneWidget);
      expect(find.byKey(const Key('forgot_password_button')), findsOneWidget);
      expect(find.byKey(const Key('facebook_button')), findsOneWidget);
      expect(find.byKey(const Key('apple_button')), findsOneWidget);
      expect(find.text('Ghi nhớ tài khoản'), findsOneWidget);
      expect(find.text('Quên mật khẩu?'), findsOneWidget);

      print('✅ Complete login form layout renders correctly');
    });

    testWidgets('🔍 Form validation simulation', (WidgetTester tester) async {
      // Arrange - Tạo form với validation
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: Column(
                children: [
                  TextFormField(
                    key: const Key('email_field'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Email không được để trống';
                      }
                      if (!value.contains('@')) {
                        return 'Email không hợp lệ';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    key: const Key('password_field'),
                    validator: (value) {
                      if (value == null || value.length < 6) {
                        return 'Mật khẩu phải có ít nhất 6 ký tự';
                      }
                      return null;
                    },
                  ),
                  ElevatedButton(
                    onPressed: () {
                      formKey.currentState?.validate();
                    },
                    child: const Text('Validate'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Act - Submit form trống
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();

      // Assert - Validation errors hiển thị
      expect(find.text('Email không được để trống'), findsOneWidget);
      expect(find.text('Mật khẩu phải có ít nhất 6 ký tự'), findsOneWidget);

      // Act - Nhập email không hợp lệ
      await tester.enterText(find.byKey(const Key('email_field')), 'invalid-email');
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();

      // Assert - Email validation error
      expect(find.text('Email không hợp lệ'), findsOneWidget);

      print('✅ Form validation works correctly');
    });

    testWidgets('👁️ Password visibility toggle', (WidgetTester tester) async {
      // Arrange - Tạo password field với visibility toggle
      bool isPasswordVisible = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return TextFormField(
                  key: const Key('password_field'),
                  obscureText: !isPasswordVisible,
                  decoration: InputDecoration(
                    labelText: 'Mật khẩu',
                    suffixIcon: IconButton(
                      key: const Key('visibility_toggle'),
                      icon: Icon(
                        isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          isPasswordVisible = !isPasswordVisible;
                        });
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      );

      // Assert initial state - password hidden
      TextFormField passwordField = tester.widget(find.byKey(const Key('password_field')));
      expect(passwordField.obscureText, isTrue);
 
      // Act - Toggle visibility
      await tester.tap(find.byKey(const Key('visibility_toggle')));
      await tester.pumpAndSettle();

      // Assert - password now visible
      passwordField = tester.widget(find.byKey(const Key('password_field')));
      expect(passwordField.obscureText, isFalse);

      print('✅ Password visibility toggle works correctly');
    });

    testWidgets('⏳ Loading button state', (WidgetTester tester) async {
      // Arrange - Tạo button với loading state
      bool isLoading = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return ElevatedButton(
                  key: const Key('login_button'),
                  onPressed: isLoading ? null : () {
                    setState(() {
                      isLoading = true;
                    });
                    // Simulate API call
                    Future.delayed(const Duration(seconds: 2), () {
                      setState(() {
                        isLoading = false;
                      });
                    });
                  },
                  child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Đăng nhập'),
                );
              },
            ),
          ),
        ),
      );

      // Assert initial state - not loading
      expect(find.text('Đăng nhập'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsNothing);

      // Act - Tap button to start loading
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();

      // Assert - loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Đăng nhập'), findsNothing);

      print('✅ Loading button state works correctly');
    });

    testWidgets('🚨 Error message display', (WidgetTester tester) async {
      // Arrange - Tạo form với error message
      String? errorMessage;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    TextFormField(
                      key: const Key('email_field'),
                      decoration: InputDecoration(
                        labelText: 'Email',
                        errorText: errorMessage,
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          errorMessage = 'Email không hợp lệ';
                        });
                      },
                      child: const Text('Show Error'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          errorMessage = null;
                        });
                      },
                      child: const Text('Clear Error'),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Assert initial state - no error
      expect(find.text('Email không hợp lệ'), findsNothing);

      // Act - Show error
      await tester.tap(find.text('Show Error'));
      await tester.pumpAndSettle();

      // Assert - error displayed
      expect(find.text('Email không hợp lệ'), findsOneWidget);

      // Act - Clear error
      await tester.tap(find.text('Clear Error'));
      await tester.pumpAndSettle();

      // Assert - error cleared
      expect(find.text('Email không hợp lệ'), findsNothing);

      print('✅ Error message display works correctly');
    });

    testWidgets('📋 Multiple form validation scenarios', (WidgetTester tester) async {
      // Arrange - Tạo form với nhiều validation rules
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: Column(
                children: [
                  // Email field với nhiều validation
                  TextFormField(
                    key: const Key('email_field'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Email không được để trống';
                      }
                      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value)) {
                        return 'Định dạng email không hợp lệ';
                      }
                      if (value.length > 50) {
                        return 'Email quá dài (tối đa 50 ký tự)';
                      }
                      return null;
                    },
                  ),
                  // Password field với validation phức tạp
                  TextFormField(
                    key: const Key('password_field'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Mật khẩu không được để trống';
                      }
                      if (value.length < 6) {
                        return 'Mật khẩu phải có ít nhất 6 ký tự';
                      }
                      if (value.length > 20) {
                        return 'Mật khẩu quá dài (tối đa 20 ký tự)';
                      }
                      if (!RegExp(r'^(?=.*[a-zA-Z])(?=.*\d)').hasMatch(value)) {
                        return 'Mật khẩu phải chứa cả chữ và số';
                      }
                      return null;
                    },
                  ),
                  ElevatedButton(
                    onPressed: () {
                      formKey.currentState?.validate();
                    },
                    child: const Text('Validate'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Test Case 1: Empty form
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Email không được để trống'), findsOneWidget);
      expect(find.text('Mật khẩu không được để trống'), findsOneWidget);

      // Test Case 2: Invalid email format
      await tester.enterText(find.byKey(const Key('email_field')), 'invalid-email');
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Định dạng email không hợp lệ'), findsOneWidget);

      // Test Case 3: Email too long
      await tester.enterText(find.byKey(const Key('email_field')),
          '<EMAIL>');
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Email quá dài (tối đa 50 ký tự)'), findsOneWidget);

      // Test Case 4: Password too short
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), '123');
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Mật khẩu phải có ít nhất 6 ký tự'), findsOneWidget);

      // Test Case 5: Password without letters/numbers
      await tester.enterText(find.byKey(const Key('password_field')), '!!!!!!');
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Mật khẩu phải chứa cả chữ và số'), findsOneWidget);

      // Test Case 6: Valid form
      await tester.enterText(find.byKey(const Key('password_field')), 'abc123');
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      // No error messages should be visible
      expect(find.textContaining('không được để trống'), findsNothing);
      expect(find.textContaining('không hợp lệ'), findsNothing);

      print('✅ Multiple form validation scenarios work correctly');
    });

    testWidgets('🎨 UI theme and styling', (WidgetTester tester) async {
      // Arrange - Tạo form với custom theme
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            primarySwatch: Colors.blue,
            inputDecorationTheme: const InputDecorationTheme(
              border: OutlineInputBorder(),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.blue, width: 2),
              ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
          home: Scaffold(
            body: Column(
              children: [
                TextFormField(
                  key: const Key('styled_email_field'),
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    prefixIcon: Icon(Icons.email),
                    helperText: 'Nhập địa chỉ email của bạn',
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    key: const Key('styled_button'),
                    onPressed: () {},
                    child: const Text('ĐĂNG NHẬP'),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // Assert - Check styled elements exist
      expect(find.byKey(const Key('styled_email_field')), findsOneWidget);
      expect(find.byKey(const Key('styled_button')), findsOneWidget);
      expect(find.text('Nhập địa chỉ email của bạn'), findsOneWidget);
      expect(find.byIcon(Icons.email), findsOneWidget);

      // Test button styling
      final button = tester.widget<ElevatedButton>(find.byKey(const Key('styled_button')));
      expect(button.child, isA<Text>());

      print('✅ UI theme and styling applied correctly');
    });

    testWidgets('📱 Responsive layout behavior', (WidgetTester tester) async {
      // Arrange - Test different screen sizes
      await tester.binding.setSurfaceSize(const Size(400, 800)); // Mobile

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  padding: EdgeInsets.all(constraints.maxWidth > 600 ? 32 : 16),
                  child: Column(
                    children: [
                      Container(
                        width: constraints.maxWidth > 600 ? 400 : double.infinity,
                        child: Column(
                          children: [
                            TextFormField(
                              key: const Key('responsive_email'),
                              decoration: const InputDecoration(labelText: 'Email'),
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                key: const Key('responsive_button'),
                                onPressed: () {},
                                child: const Text('Đăng nhập'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      // Assert mobile layout
      expect(find.byKey(const Key('responsive_email')), findsOneWidget);
      expect(find.byKey(const Key('responsive_button')), findsOneWidget);

      // Test tablet size
      await tester.binding.setSurfaceSize(const Size(800, 1200)); // Tablet
      await tester.pumpAndSettle();

      // Elements should still be present
      expect(find.byKey(const Key('responsive_email')), findsOneWidget);
      expect(find.byKey(const Key('responsive_button')), findsOneWidget);

      print('✅ Responsive layout behavior works correctly');
    });

    testWidgets('⌨️ Keyboard interaction and focus', (WidgetTester tester) async {
      // Arrange - Tạo form với focus management
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                TextFormField(
                  key: const Key('email_field'),
                  decoration: const InputDecoration(labelText: 'Email'),
                  textInputAction: TextInputAction.next,
                ),
                TextFormField(
                  key: const Key('password_field'),
                  decoration: const InputDecoration(labelText: 'Password'),
                  textInputAction: TextInputAction.done,
                ),
                ElevatedButton(
                  key: const Key('submit_button'),
                  onPressed: () {},
                  child: const Text('Submit'),
                ),
              ],
            ),
          ),
        ),
      );

      // Act - Focus on email field
      await tester.tap(find.byKey(const Key('email_field')));
      await tester.pumpAndSettle();

      // Assert - Email field has focus
      final emailField = tester.widget<TextFormField>(find.byKey(const Key('email_field')));
      expect(emailField.textInputAction, TextInputAction.next);

      // Act - Enter text and move to next field
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.testTextInput.receiveAction(TextInputAction.next);
      await tester.pumpAndSettle();

      // Password field should now have focus (in real app)
      final passwordField = tester.widget<TextFormField>(find.byKey(const Key('password_field')));
      expect(passwordField.textInputAction, TextInputAction.done);

      print('✅ Keyboard interaction and focus management works');
    });

    testWidgets('🔄 Form reset functionality', (WidgetTester tester) async {
      // Arrange - Tạo form với reset capability
      final emailController = TextEditingController();
      final passwordController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                TextFormField(
                  key: const Key('email_field'),
                  controller: emailController,
                  decoration: const InputDecoration(labelText: 'Email'),
                ),
                TextFormField(
                  key: const Key('password_field'),
                  controller: passwordController,
                  decoration: const InputDecoration(labelText: 'Password'),
                ),
                ElevatedButton(
                  key: const Key('reset_button'),
                  onPressed: () {
                    emailController.clear();
                    passwordController.clear();
                  },
                  child: const Text('Reset'),
                ),
              ],
            ),
          ),
        ),
      );

      // Act - Fill form
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      await tester.pumpAndSettle();

      // Assert - Form has data
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('password123'), findsOneWidget);

      // Act - Reset form
      await tester.tap(find.byKey(const Key('reset_button')));
      await tester.pumpAndSettle();

      // Assert - Form is cleared
      expect(emailController.text, isEmpty);
      expect(passwordController.text, isEmpty);

      print('✅ Form reset functionality works correctly');
    });
  });
}
