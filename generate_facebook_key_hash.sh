#!/bin/bash

# 🔑 Facebook Key Hash Generator for Android
# Generates key hash needed for Facebook login on Android

echo "🔑 ====================================="
echo "🔑 FACEBOOK KEY HASH GENERATOR"
echo "🔑 ====================================="
echo ""

# Check if keytool is available
if ! command -v keytool &> /dev/null; then
    echo "❌ keytool not found. Please install Java JDK."
    exit 1
fi

# Check if openssl is available
if ! command -v openssl &> /dev/null; then
    echo "❌ openssl not found. Please install OpenSSL."
    exit 1
fi

echo "📱 Generating Facebook key hash for Android..."
echo ""

# Default debug keystore path
DEBUG_KEYSTORE="$HOME/.android/debug.keystore"

# Check if debug keystore exists
if [ ! -f "$DEBUG_KEYSTORE" ]; then
    echo "❌ Debug keystore not found at: $DEBUG_KEYSTORE"
    echo "💡 Please run 'flutter run' once to generate debug keystore"
    exit 1
fi

echo "🔍 Using debug keystore: $DEBUG_KEYSTORE"
echo ""

# Generate key hash
echo "🔄 Generating key hash..."
KEY_HASH=$(keytool -exportcert -alias androiddebugkey -keystore "$DEBUG_KEYSTORE" -storepass android -keypass android 2>/dev/null | openssl sha1 -binary | openssl base64)

if [ $? -eq 0 ] && [ ! -z "$KEY_HASH" ]; then
    echo "✅ Key hash generated successfully!"
    echo ""
    echo "🔑 ====================================="
    echo "🔑 YOUR FACEBOOK KEY HASH:"
    echo "🔑 ====================================="
    echo "🔑 $KEY_HASH"
    echo "🔑 ====================================="
    echo ""
    echo "📋 NEXT STEPS:"
    echo "1. Copy the key hash above"
    echo "2. Go to https://developers.facebook.com/"
    echo "3. Select app: 367174740769877"
    echo "4. Settings → Basic → Android"
    echo "5. Paste into 'Key Hashes' field"
    echo "6. Save changes"
    echo ""
    echo "📱 Package Name: com.beta.betacineplex"
    echo "🆔 Facebook App ID: 367174740769877"
    echo ""
    echo "✅ After adding key hash, Facebook login should work on Android!"
else
    echo "❌ Failed to generate key hash"
    echo "💡 Make sure you have run 'flutter run' at least once"
    exit 1
fi
