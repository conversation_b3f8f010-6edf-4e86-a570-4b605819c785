# 🔧 Fix Test Errors - Hướng dẫn sửa lỗi Testing

## ❌ Lỗi đã gặp và cách fix

### 1. Lỗi "Undefined class 'MockApi', 'MockAuthC'"

**Nguyên nhân:** File test sử dụng `@GenerateMocks` nhưng chưa generate mock classes.

**✅ Cách fix:**

#### Option 1: Sử dụng Manual Mocks (Đơn giản)
```dart
// Thay vì dùng @GenerateMocks
import 'package:mockito/mockito.dart';

// Tạo mock classes thủ công
class MockApi extends Mock implements Api {}
class MockAuthC extends Mock implements AuthC {}
```

#### Option 2: Generate Mocks (Nâng cao)
```bash
# 1. Thêm build_runner vào pubspec.yaml
dev_dependencies:
  build_runner: ^2.4.7
  mockito: ^5.4.2

# 2. Generate mocks
flutter packages pub run build_runner build

# 3. Import generated file
import 'login_widget_test.mocks.dart';
```

### 2. Lỗi Import Path

**❌ Lỗi:**
```dart
import 'package:flutter_app/pages/access/login.dart';
```

**✅ Fix:**
```dart
import '../../lib/pages/access/login.dart';
```

### 3. Lỗi BLoC Provider Setup

**❌ Lỗi:**
```dart
MultiBlocProvider(
  providers: [
    BlocProvider<AuthC>.value(value: mockAuthC),
    RepositoryProvider<Api>.value(value: mockApi),
  ],
  child: LoginPage(),
)
```

**✅ Fix:**
```dart
MultiRepositoryProvider(
  providers: [
    RepositoryProvider<Api>.value(value: mockApi),
  ],
  child: MultiBlocProvider(
    providers: [
      BlocProvider<AuthC>.value(value: mockAuthC),
    ],
    child: LoginPage(),
  ),
)
```

## 🚀 Files đã fix và sẵn sàng chạy

### 1. `test/simple_login_widget_test.dart` ✅
- **Mô tả:** Widget test đơn giản, không cần dependencies phức tạp
- **Chạy:** `flutter test test/simple_login_widget_test.dart`
- **Nội dung:** Test các UI components cơ bản

### 2. `test/demo_login_test.dart` ✅
- **Mô tả:** Integration test hoàn chỉnh với real app
- **Chạy:** `flutter test test/demo_login_test.dart`
- **Nội dung:** 8 test cases từ cơ bản đến nâng cao

### 3. `test/widget/login_widget_test.dart` ⚠️
- **Trạng thái:** Đã fix lỗi cơ bản, có thể cần điều chỉnh thêm
- **Chạy:** `flutter test test/widget/login_widget_test.dart`

## 📋 Checklist trước khi chạy tests

### ✅ Dependencies
```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.2
  build_runner: ^2.4.7 # Nếu dùng @GenerateMocks
```

### ✅ Commands
```bash
# 1. Cài dependencies
flutter pub get

# 2. Generate mocks (nếu cần)
flutter packages pub run build_runner build

# 3. Chạy tests
flutter test
```

## 🎯 Khuyến nghị chạy tests theo thứ tự

### 1. Bắt đầu với Simple Widget Test
```bash
flutter test test/simple_login_widget_test.dart --reporter=expanded
```
**Tại sao:** Đơn giản nhất, ít dependencies, dễ debug

### 2. Chạy Demo Integration Test
```bash
flutter test test/demo_login_test.dart --reporter=expanded
```
**Tại sao:** Test real app flow, có console logs chi tiết

### 3. Chạy Advanced Tests (nếu cần)
```bash
flutter test test/widget/login_widget_test.dart
flutter test test/login_advanced_test.dart
```

## 🐛 Troubleshooting thêm

### Lỗi "No MaterialLocalizations found"
```dart
// Fix: Wrap với MaterialApp
await tester.pumpWidget(
  MaterialApp(
    home: YourWidget(),
  ),
);
```

### Lỗi "RenderFlex overflowed"
```dart
// Fix: Thêm SingleChildScrollView
body: SingleChildScrollView(
  child: Column(children: [...]),
)
```

### Lỗi "Null check operator used on a null value"
```dart
// Fix: Kiểm tra null trước khi sử dụng
if (widget != null) {
  // Use widget
}
```

### Lỗi "Widget not found"
```dart
// Fix: Thêm delay để đợi UI render
await tester.pumpAndSettle();
await Future.delayed(Duration(milliseconds: 100));
```

## 📊 Expected Test Results

### Simple Widget Test
```
✓ Email field should accept text input
✓ Password field should hide text  
✓ Login button should be tappable
✓ Checkbox should toggle state
✓ Complete login form layout
✓ Form validation simulation
```

### Demo Integration Test
```
✓ Test 1: Kiểm tra UI elements có hiển thị đúng không
✓ Test 2: Kiểm tra validation khi form trống
✓ Test 3: Kiểm tra validation email không hợp lệ
✓ Test 4: Thử đăng nhập với tài khoản không tồn tại
✓ Test 5: Thử đăng nhập với mật khẩu sai
✓ Test 6: Đăng nhập thành công
✓ Test 7: Kiểm tra điều hướng "Quên mật khẩu"
✓ Test 8: Kiểm tra điều hướng "Đăng ký"
```

## 💡 Tips để tránh lỗi

1. **Luôn wrap widgets với MaterialApp** khi test
2. **Sử dụng keys** để tìm widgets dễ dàng
3. **Thêm pumpAndSettle()** sau mỗi action
4. **Check widget tồn tại** trước khi interact
5. **Sử dụng try-catch** cho error handling
6. **Mock dependencies** đúng cách
7. **Test từ đơn giản đến phức tạp**

## 🎉 Kết luận

Sau khi fix các lỗi trên, bạn có thể chạy:

```bash
# Test đơn giản nhất (khuyến nghị bắt đầu)
flutter test test/simple_login_widget_test.dart

# Test integration hoàn chỉnh
flutter test test/demo_login_test.dart

# Tất cả tests
flutter test
```

**Happy Testing!** 🧪✨
