import 'package:flutter/material.dart';
import 'package:flutter_app/utils/src/api.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:flutter_app/pages/access/login.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/service/index.dart';

// Generate mocks
@GenerateMocks([Api, AuthC])
/// Widget Testing cho Login Page
///
/// Widget tests tập trung vào testing các component riêng lẻ
/// mà không cần khởi tạo toàn bộ app
void main() {
  group('Login Widget Tests', () {
    late MockApi mockApi;
    late MockAuthC mockAuthC;

    setUp(() {
      mockApi = MockApi();
      mockAuthC = MockAuthC();
    });

    Widget createLoginWidget() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<AuthC>.value(value: mockAuthC),
            RepositoryProvider<Api>.value(value: mockApi),
          ],
          child: const LoginPage(),
        ),
      );
    }

    testWidgets('Login page renders correctly - Kiểm tra render đúng', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Verify key UI elements
      expect(find.text('Đăng nhập'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password fields
      expect(find.text('Đăng nhập với Facebook'), findsOneWidget);
      expect(find.text('Đăng nhập với Apple'), findsOneWidget);
      expect(find.text('Quên mật khẩu?'), findsOneWidget);
    });

    testWidgets('Email field validation - Kiểm tra validation email', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      final emailField = find.byKey(const ValueKey('email_field'));
      final loginButton = find.text('Đăng nhập');

      // Test empty email
      await tester.tap(loginButton);
      await tester.pumpAndSettle();
      expect(find.text('Email is required'), findsOneWidget);

      // Test invalid email format
      await tester.enterText(emailField, 'invalid-email');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();
      expect(find.text('Invalid email format'), findsOneWidget);

      // Test valid email
      await tester.enterText(emailField, '<EMAIL>');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();
      expect(find.text('Invalid email format'), findsNothing);
    });

    testWidgets('Password field validation - Kiểm tra validation password', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      final passwordField = find.byKey(const ValueKey('password_field'));
      final loginButton = find.text('Đăng nhập');

      // Test empty password
      await tester.tap(loginButton);
      await tester.pumpAndSettle();
      expect(find.text('Password is required'), findsOneWidget);

      // Test short password
      await tester.enterText(passwordField, '123');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();
      expect(find.text('Password must be at least 6 characters'), findsOneWidget);

      // Test valid password
      await tester.enterText(passwordField, '123456');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();
      expect(find.text('Password must be at least 6 characters'), findsNothing);
    });

    testWidgets('Loading state during login - Kiểm tra trạng thái loading', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Fill in valid credentials
      await tester.enterText(find.byKey(const ValueKey('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const ValueKey('password_field')), '123456');

      // Tap login button
      await tester.tap(find.text('Đăng nhập'));
      await tester.pump(); // Don't use pumpAndSettle to catch loading state

      // Verify loading indicator appears
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Social login buttons are tappable - Kiểm tra nút đăng nhập MXH', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Test Facebook button
      final facebookButton = find.text('Đăng nhập với Facebook');
      expect(facebookButton, findsOneWidget);
      await tester.tap(facebookButton);
      await tester.pumpAndSettle();

      // Test Apple button
      final appleButton = find.text('Đăng nhập với Apple');
      expect(appleButton, findsOneWidget);
      await tester.tap(appleButton);
      await tester.pumpAndSettle();
    });

    testWidgets('Remember me checkbox functionality - Kiểm tra checkbox ghi nhớ', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      final rememberMeCheckbox = find.byType(Checkbox);
      expect(rememberMeCheckbox, findsOneWidget);

      // Initial state should be unchecked
      Checkbox checkbox = tester.widget(rememberMeCheckbox);
      expect(checkbox.value, false);

      // Tap to check
      await tester.tap(rememberMeCheckbox);
      await tester.pumpAndSettle();

      // Verify state changed
      checkbox = tester.widget(rememberMeCheckbox);
      expect(checkbox.value, true);
    });

    testWidgets('Navigation to forgot password - Kiểm tra điều hướng quên mật khẩu', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      final forgotPasswordLink = find.text('Quên mật khẩu?');
      expect(forgotPasswordLink, findsOneWidget);

      await tester.tap(forgotPasswordLink);
      await tester.pumpAndSettle();

      // Verify navigation occurred (this would need router setup in real test)
    });

    testWidgets('Navigation to register page - Kiểm tra điều hướng đăng ký', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      final registerLink = find.text('Đăng ký');
      expect(registerLink, findsOneWidget);

      await tester.tap(registerLink);
      await tester.pumpAndSettle();

      // Verify navigation occurred
    });

    testWidgets('Error message display - Kiểm tra hiển thị thông báo lỗi', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Simulate login error
      // This would require mocking the BLoC state
      // For now, we just verify error message widgets can be found

      // Test for potential error message containers
      expect(find.byType(SnackBar), findsNothing); // No error initially
    });

    testWidgets('Form submission with valid data - Kiểm tra submit form hợp lệ', (WidgetTester tester) async {
      await tester.pumpWidget(createLoginWidget());
      await tester.pumpAndSettle();

      // Fill form with valid data
      await tester.enterText(find.byKey(const ValueKey('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const ValueKey('password_field')), '123456');

      // Submit form
      await tester.tap(find.text('Đăng nhập'));
      await tester.pumpAndSettle();

      // Verify API call was made (would need proper mocking)
      // verify(mockApi.auth.login(any)).called(1);
    });
  });
}
