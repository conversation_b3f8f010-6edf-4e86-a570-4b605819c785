import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:faker/faker.dart';
/// 🎨 Widget Testing cho Login Components
///
/// Sử dụng flutter_test và faker để test UI components
/// Không cần mockito hay dependencies phức tạp
void main() {
  group('🎨 Login Widget Tests với Faker', () {
    late Faker faker;

    setUp(() {
      faker = Faker();
    });

    testWidgets('✅ Login form UI elements render correctly', (WidgetTester tester) async {
      // Arrange - Tạo login form đơn giản
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: const Text('Đăng nhập')),
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Logo hoặc title
                  const Icon(Icons.account_circle, size: 80),
                  const SizedBox(height: 24),
                  const Text(
                    '<PERSON><PERSON><PERSON> mừng trở lại!',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 32),

                  // Email field
                  TextFormField(
                    key: const Key('email_field'),
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      prefixIcon: Icon(Icons.email),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),

                  // Password field
                  TextFormField(
                    key: const Key('password_field'),
                    obscureText: true,
                    decoration: const InputDecoration(
                      labelText: 'Mật khẩu',
                      prefixIcon: Icon(Icons.lock),
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Login button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      key: const Key('login_button'),
                      onPressed: () {},
                      child: const Text('Đăng nhập'),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Forgot password
                  TextButton(
                    key: const Key('forgot_password'),
                    onPressed: () {},
                    child: const Text('Quên mật khẩu?'),
                  ),

                  // Social login
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          key: const Key('facebook_button'),
                          onPressed: () {},
                          icon: const Icon(Icons.facebook),
                          label: const Text('Facebook'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          key: const Key('apple_button'),
                          onPressed: () {},
                          icon: const Icon(Icons.apple),
                          label: const Text('Apple'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert - Verify all UI elements exist
      expect(find.text('Đăng nhập'), findsNWidgets(2)); // Title + Button
      expect(find.text('Chào mừng trở lại!'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.byIcon(Icons.email), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget);
      expect(find.text('Quên mật khẩu?'), findsOneWidget);
      expect(find.text('Facebook'), findsOneWidget);
      expect(find.text('Apple'), findsOneWidget);

      // Test password field is obscured - check constructor parameter
      final passwordField = tester.widget<TextFormField>(find.byKey(const Key('password_field')));
      // TextFormField doesn't expose obscureText as getter, but we can verify it was set in constructor
      // Alternative: check if password text is hidden by trying to find the actual text
      await tester.enterText(find.byKey(const Key('password_field')), 'testpassword');
      await tester.pumpAndSettle();
      // If obscureText is true, the actual text won't be visible, only dots/asterisks
      expect(find.text('testpassword'), findsNothing); // Password should be hidden

      print('✅ All UI elements render correctly');
    });

    testWidgets('📧 Email field validation với Faker data', (WidgetTester tester) async {
      // Arrange - Tạo form với validation
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: Column(
                children: [
                  TextFormField(
                    key: const Key('email_field'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Email không được để trống';
                      }
                      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value)) {
                        return 'Email không hợp lệ';
                      }
                      return null;
                    },
                    decoration: const InputDecoration(labelText: 'Email'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      formKey.currentState?.validate();
                    },
                    child: const Text('Validate'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Test 1: Empty email
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Email không được để trống'), findsOneWidget);

      // Test 2: Invalid email với faker
      final invalidEmail = faker.lorem.word(); // Random word without @
      await tester.enterText(find.byKey(const Key('email_field')), invalidEmail);
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Email không hợp lệ'), findsOneWidget);

      // Test 3: Valid email với faker
      final validEmail = faker.internet.email();
      await tester.enterText(find.byKey(const Key('email_field')), validEmail);
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Email không hợp lệ'), findsNothing);
      expect(find.text('Email không được để trống'), findsNothing);

      print('✅ Email validation works with faker data: $validEmail');
    });

    testWidgets('🔒 Password field validation với Faker data', (WidgetTester tester) async {
      // Arrange - Tạo form với password validation
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: Column(
                children: [
                  TextFormField(
                    key: const Key('password_field'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Mật khẩu không được để trống';
                      }
                      if (value.length < 6) {
                        return 'Mật khẩu phải có ít nhất 6 ký tự';
                      }
                      if (value.length > 20) {
                        return 'Mật khẩu quá dài (tối đa 20 ký tự)';
                      }
                      return null;
                    },
                    decoration: const InputDecoration(labelText: 'Mật khẩu'),
                    obscureText: true,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      formKey.currentState?.validate();
                    },
                    child: const Text('Validate'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Test 1: Empty password
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Mật khẩu không được để trống'), findsOneWidget);

      // Test 2: Short password với faker
      final shortPassword = faker.randomGenerator.string(3); // 3 characters
      await tester.enterText(find.byKey(const Key('password_field')), shortPassword);
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Mật khẩu phải có ít nhất 6 ký tự'), findsOneWidget);

      // Test 3: Too long password với faker
      final longPassword = faker.randomGenerator.string(25); // 25 characters
      await tester.enterText(find.byKey(const Key('password_field')), longPassword);
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Mật khẩu quá dài (tối đa 20 ký tự)'), findsOneWidget);

      // Test 4: Valid password với faker
      final validPassword = faker.randomGenerator.string(8); // 8 characters
      await tester.enterText(find.byKey(const Key('password_field')), validPassword);
      await tester.tap(find.text('Validate'));
      await tester.pumpAndSettle();
      expect(find.text('Mật khẩu không được để trống'), findsNothing);
      expect(find.text('Mật khẩu phải có ít nhất 6 ký tự'), findsNothing);
      expect(find.text('Mật khẩu quá dài (tối đa 20 ký tự)'), findsNothing);

      print('✅ Password validation works with faker data: ${validPassword.length} chars');
    });

    testWidgets('🎲 Multiple test scenarios với Faker data', (WidgetTester tester) async {
      // Arrange - Tạo login form hoàn chỉnh
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                TextFormField(
                  key: const Key('email_field'),
                  decoration: const InputDecoration(labelText: 'Email'),
                ),
                TextFormField(
                  key: const Key('password_field'),
                  decoration: const InputDecoration(labelText: 'Password'),
                  obscureText: true,
                ),
                TextFormField(
                  key: const Key('name_field'),
                  decoration: const InputDecoration(labelText: 'Full Name'),
                ),
                TextFormField(
                  key: const Key('phone_field'),
                  decoration: const InputDecoration(labelText: 'Phone'),
                ),
                ElevatedButton(
                  key: const Key('submit_button'),
                  onPressed: () {},
                  child: const Text('Submit'),
                ),
              ],
            ),
          ),
        ),
      );

      // Test với nhiều bộ dữ liệu faker khác nhau
      for (int i = 0; i < 3; i++) {
        print('🧪 Test scenario ${i + 1}:');

        // Generate fake data
        final email = faker.internet.email();
        final password = faker.internet.password(length: 8);
        final fullName = faker.person.name();
        final phone = faker.phoneNumber.us();

        print('  📧 Email: $email');
        print('  🔒 Password: $password');
        print('  👤 Name: $fullName');
        print('  📱 Phone: $phone');

        // Act - Fill form với faker data
        await tester.enterText(find.byKey(const Key('email_field')), email);
        await tester.enterText(find.byKey(const Key('password_field')), password);
        await tester.enterText(find.byKey(const Key('name_field')), fullName);
        await tester.enterText(find.byKey(const Key('phone_field')), phone);
        await tester.pumpAndSettle();

        // Assert - Verify data was entered
        expect(find.text(email), findsOneWidget);
        expect(find.text(fullName), findsOneWidget);
        expect(find.text(phone), findsOneWidget);

        // Password is hidden but controller should have value
        final passwordField = tester.widget<TextFormField>(find.byKey(const Key('password_field')));
        expect(passwordField.controller?.text, equals(password));

        // Clear form for next iteration
        await tester.enterText(find.byKey(const Key('email_field')), '');
        await tester.enterText(find.byKey(const Key('password_field')), '');
        await tester.enterText(find.byKey(const Key('name_field')), '');
        await tester.enterText(find.byKey(const Key('phone_field')), '');
        await tester.pumpAndSettle();
      }

      print('✅ Multiple test scenarios completed with faker data');
    });

    testWidgets('🌍 Internationalization test với Faker locales', (WidgetTester tester) async {
      // Test với các locale khác nhau
      final locales = ['vi', 'en', 'ja', 'ko'];

      for (final locale in locales) {
        print('🌍 Testing with locale: $locale');

        // Tạo faker với locale cụ thể
        final localeFaker = Faker.withGenerator(RandomGenerator(seed: 42));

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  TextFormField(
                    key: Key('name_field_$locale'),
                    decoration: InputDecoration(
                      labelText: 'Name ($locale)',
                      hintText: 'Enter your name',
                    ),
                  ),
                  TextFormField(
                    key: Key('address_field_$locale'),
                    decoration: InputDecoration(
                      labelText: 'Address ($locale)',
                      hintText: 'Enter your address',
                    ),
                  ),
                ],
              ),
            ),
          ),
        );

        // Generate data với faker
        final name = localeFaker.person.name();
        final address = localeFaker.address.streetAddress();

        print('  👤 Name: $name');
        print('  🏠 Address: $address');

        // Test input
        await tester.enterText(find.byKey(Key('name_field_$locale')), name);
        await tester.enterText(find.byKey(Key('address_field_$locale')), address);
        await tester.pumpAndSettle();

        // Verify
        expect(find.text(name), findsOneWidget);
        expect(find.text(address), findsOneWidget);
      }

      print('✅ Internationalization test completed');
    });

    testWidgets('👁️ Password visibility toggle với Faker password', (WidgetTester tester) async {
      // Arrange - Tạo password field với visibility toggle
      bool isPasswordVisible = false;
      final testPassword = faker.internet.password(length: 10);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    TextFormField(
                      key: const Key('password_field'),
                      obscureText: !isPasswordVisible,
                      decoration: InputDecoration(
                        labelText: 'Mật khẩu',
                        suffixIcon: IconButton(
                          key: const Key('visibility_toggle'),
                          icon: Icon(
                            isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              isPasswordVisible = !isPasswordVisible;
                            });
                          },
                        ),
                      ),
                    ),
                    Text('Password visible: $isPasswordVisible'),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Enter faker password
      await tester.enterText(find.byKey(const Key('password_field')), testPassword);
      await tester.pumpAndSettle();

      // Assert initial state - password hidden
      expect(find.text('Password visible: false'), findsOneWidget);
      // Test that password text is hidden
      expect(find.text(testPassword), findsNothing);

      // Act - Toggle visibility
      await tester.tap(find.byKey(const Key('visibility_toggle')));
      await tester.pumpAndSettle();

      // Assert - password now visible
      expect(find.text('Password visible: true'), findsOneWidget);
      // When visible, we should be able to see the password text
      expect(find.text(testPassword), findsOneWidget);

      // Act - Toggle back to hidden
      await tester.tap(find.byKey(const Key('visibility_toggle')));
      await tester.pumpAndSettle();

      // Assert - password hidden again
      expect(find.text('Password visible: false'), findsOneWidget);
      expect(find.text(testPassword), findsNothing);

      print('✅ Password visibility toggle works with faker password: ${testPassword.substring(0, 3)}***');
    });

    testWidgets('🔐 Password obscureText behavior test', (WidgetTester tester) async {
      // Arrange - Tạo 2 TextFormField để so sánh: 1 obscured, 1 normal
      final testPassword = faker.internet.password(length: 8);
      final normalController = TextEditingController();
      final obscuredController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // Normal text field (password visible)
                TextFormField(
                  key: const Key('normal_field'),
                  controller: normalController,
                  decoration: const InputDecoration(labelText: 'Normal Text'),
                ),
                // Obscured text field (password hidden)
                TextFormField(
                  key: const Key('obscured_field'),
                  controller: obscuredController,
                  obscureText: true,
                  decoration: const InputDecoration(labelText: 'Obscured Text'),
                ),
                const SizedBox(height: 20),
                const Text('Test Results:'),
              ],
            ),
          ),
        ),
      );

      // Act - Enter same text in both fields
      await tester.enterText(find.byKey(const Key('normal_field')), testPassword);
      await tester.enterText(find.byKey(const Key('obscured_field')), testPassword);
      await tester.pumpAndSettle();

      // Assert - Normal field shows text visually, obscured field hides it
      expect(find.text(testPassword), findsOneWidget); // Only visible in normal field

      // Both controllers should have the same actual value
      expect(normalController.text, equals(testPassword));
      expect(obscuredController.text, equals(testPassword));

      // Verify that we can find the text in normal field but not in obscured field
      final normalFieldFinder = find.descendant(
        of: find.byKey(const Key('normal_field')),
        matching: find.text(testPassword),
      );
      expect(normalFieldFinder, findsOneWidget);

      print('✅ Password obscureText behavior verified');
      print('  📝 Normal field controller: ${normalController.text}');
      print('  🔒 Obscured field controller: ${obscuredController.text}');
      print('  👁️ Visual difference: Normal shows text, obscured shows dots');
    });

    testWidgets('⏳ Loading button state với Faker credentials', (WidgetTester tester) async {
      // Arrange - Tạo button với loading state
      bool isLoading = false;
      final email = faker.internet.email();
      final password = faker.internet.password();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    TextFormField(
                      key: const Key('email_field'),
                      decoration: const InputDecoration(labelText: 'Email'),
                    ),
                    TextFormField(
                      key: const Key('password_field'),
                      decoration: const InputDecoration(labelText: 'Password'),
                    ),
                    ElevatedButton(
                      key: const Key('login_button'),
                      onPressed: isLoading ? null : () {
                        setState(() {
                          isLoading = true;
                        });
                        // Simulate API call
                        Future.delayed(const Duration(seconds: 2), () {
                          if (context.mounted) {
                            setState(() {
                              isLoading = false;
                            });
                          }
                        });
                      },
                      child: isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Đăng nhập'),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Fill in faker credentials
      await tester.enterText(find.byKey(const Key('email_field')), email);
      await tester.enterText(find.byKey(const Key('password_field')), password);
      await tester.pumpAndSettle();

      // Assert initial state - not loading
      expect(find.text('Đăng nhập'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsNothing);

      // Act - Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pump(); // Don't use pumpAndSettle to catch loading state

      // Assert - loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Đăng nhập'), findsNothing);

      print('✅ Loading state works with faker credentials: $email');
    });

    testWidgets('📱 Social login buttons functionality', (WidgetTester tester) async {
      // Arrange - Tạo social login buttons
      String? selectedProvider;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ElevatedButton.icon(
                  key: const Key('facebook_button'),
                  onPressed: () {
                    selectedProvider = 'Facebook';
                  },
                  icon: const Icon(Icons.facebook),
                  label: const Text('Đăng nhập với Facebook'),
                ),
                ElevatedButton.icon(
                  key: const Key('apple_button'),
                  onPressed: () {
                    selectedProvider = 'Apple';
                  },
                  icon: const Icon(Icons.apple),
                  label: const Text('Đăng nhập với Apple'),
                ),
                ElevatedButton.icon(
                  key: const Key('google_button'),
                  onPressed: () {
                    selectedProvider = 'Google';
                  },
                  icon: const Icon(Icons.g_mobiledata),
                  label: const Text('Đăng nhập với Google'),
                ),
                if (selectedProvider != null)
                  Text('Selected: $selectedProvider'),
              ],
            ),
          ),
        ),
      );

      // Test Facebook button
      await tester.tap(find.byKey(const Key('facebook_button')));
      await tester.pumpAndSettle();
      expect(find.text('Selected: Facebook'), findsOneWidget);

      // Test Apple button
      await tester.tap(find.byKey(const Key('apple_button')));
      await tester.pumpAndSettle();
      expect(find.text('Selected: Apple'), findsOneWidget);

      // Test Google button
      await tester.tap(find.byKey(const Key('google_button')));
      await tester.pumpAndSettle();
      expect(find.text('Selected: Google'), findsOneWidget);

      print('✅ Social login buttons work correctly');
    });

    testWidgets('☑️ Checkbox và form controls', (WidgetTester tester) async {
      // Arrange - Tạo form với các controls
      bool rememberMe = false;
      bool acceptTerms = false;
      String selectedGender = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    CheckboxListTile(
                      key: const Key('remember_checkbox'),
                      title: const Text('Ghi nhớ đăng nhập'),
                      value: rememberMe,
                      onChanged: (value) {
                        setState(() {
                          rememberMe = value ?? false;
                        });
                      },
                    ),
                    CheckboxListTile(
                      key: const Key('terms_checkbox'),
                      title: const Text('Đồng ý điều khoản'),
                      value: acceptTerms,
                      onChanged: (value) {
                        setState(() {
                          acceptTerms = value ?? false;
                        });
                      },
                    ),
                    DropdownButton<String>(
                      key: const Key('gender_dropdown'),
                      value: selectedGender.isEmpty ? null : selectedGender,
                      hint: const Text('Chọn giới tính'),
                      items: ['Nam', 'Nữ', 'Khác'].map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedGender = newValue ?? '';
                        });
                      },
                    ),
                    Text('Remember: $rememberMe'),
                    Text('Terms: $acceptTerms'),
                    Text('Gender: $selectedGender'),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Test remember me checkbox
      await tester.tap(find.byKey(const Key('remember_checkbox')));
      await tester.pumpAndSettle();
      expect(find.text('Remember: true'), findsOneWidget);

      // Test terms checkbox
      await tester.tap(find.byKey(const Key('terms_checkbox')));
      await tester.pumpAndSettle();
      expect(find.text('Terms: true'), findsOneWidget);

      // Test dropdown
      await tester.tap(find.byKey(const Key('gender_dropdown')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Nam'));
      await tester.pumpAndSettle();
      expect(find.text('Gender: Nam'), findsOneWidget);

      print('✅ Form controls work correctly');
    });

    testWidgets('🎯 Complete login form test với Faker', (WidgetTester tester) async {
      // Arrange - Tạo complete login form
      final formKey = GlobalKey<FormState>();
      bool isSubmitted = false;
      String? submittedEmail;
      String? submittedPassword;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: const Text('Login Test')),
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: formKey,
                child: Column(
                  children: [
                    TextFormField(
                      key: const Key('email_field'),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Email required';
                        }
                        if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value)) {
                          return 'Invalid email';
                        }
                        return null;
                      },
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) => submittedEmail = value,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      key: const Key('password_field'),
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.length < 6) {
                          return 'Password min 6 chars';
                        }
                        return null;
                      },
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) => submittedPassword = value,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      key: const Key('submit_button'),
                      onPressed: () {
                        if (formKey.currentState?.validate() ?? false) {
                          formKey.currentState?.save();
                          isSubmitted = true;
                        }
                      },
                      child: const Text('Submit'),
                    ),
                    if (isSubmitted) ...[
                      const SizedBox(height: 16),
                      Text('✅ Submitted successfully!'),
                      Text('Email: $submittedEmail'),
                      Text('Password: ${submittedPassword?.substring(0, 3)}***'),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      // Generate test data với faker
      final testEmail = faker.internet.email();
      final testPassword = faker.internet.password(length: 8);

      print('🧪 Testing with faker data:');
      print('  📧 Email: $testEmail');
      print('  🔒 Password: ${testPassword.substring(0, 3)}***');

      // Act - Fill and submit form
      await tester.enterText(find.byKey(const Key('email_field')), testEmail);
      await tester.enterText(find.byKey(const Key('password_field')), testPassword);
      await tester.pumpAndSettle();

      // Validate form
      await tester.tap(find.byKey(const Key('submit_button')));
      await tester.pumpAndSettle();

      // Assert - Form submitted successfully
      expect(find.text('✅ Submitted successfully!'), findsOneWidget);
      expect(find.text('Email: $testEmail'), findsOneWidget);
      expect(find.textContaining('Password: ${testPassword.substring(0, 3)}***'), findsOneWidget);

      print('✅ Complete login form test passed with faker data');
    });
  });
}
