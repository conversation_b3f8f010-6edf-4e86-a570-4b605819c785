# 🔒 Hướng dẫn Test ObscureText trong Flutter

## ❌ Vấn đề thường gặp

### Lỗi phổ biến:
```dart
// ❌ SAI - TextFormField không có getter obscureText
final passwordField = tester.widget<TextFormField>(find.byKey(Key('password')));
expect(passwordField.obscureText, isTrue); // LỖI!
```

**Lý do:** `TextFormField` không expose `obscureText` như một getter. Nó chỉ là parameter trong constructor.

## ✅ Cách test ObscureText đúng

### 1. **Test bằng cách kiểm tra text visibility**

```dart
testWidgets('Password field hides text', (WidgetTester tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: Scaffold(
        body: TextFormField(
          key: Key('password_field'),
          obscureText: true,
          decoration: InputDecoration(labelText: 'Password'),
        ),
      ),
    ),
  );

  // Enter password
  await tester.enterText(find.byKey(Key('password_field')), 'secret123');
  await tester.pumpAndSettle();

  // ✅ ĐÚNG - Text should not be visible when obscured
  expect(find.text('secret123'), findsNothing);
});
```

### 2. **Test bằng cách so sánh với normal field**

```dart
testWidgets('Compare normal vs obscured fields', (WidgetTester tester) async {
  final testPassword = 'mypassword123';
  
  await tester.pumpWidget(
    MaterialApp(
      home: Scaffold(
        body: Column(
          children: [
            TextFormField(
              key: Key('normal_field'),
              decoration: InputDecoration(labelText: 'Normal'),
            ),
            TextFormField(
              key: Key('obscured_field'),
              obscureText: true,
              decoration: InputDecoration(labelText: 'Obscured'),
            ),
          ],
        ),
      ),
    ),
  );

  // Enter same text in both fields
  await tester.enterText(find.byKey(Key('normal_field')), testPassword);
  await tester.enterText(find.byKey(Key('obscured_field')), testPassword);
  await tester.pumpAndSettle();

  // ✅ Normal field shows text, obscured field hides it
  expect(find.text(testPassword), findsOneWidget); // Only in normal field
});
```

### 3. **Test password visibility toggle**

```dart
testWidgets('Password visibility toggle', (WidgetTester tester) async {
  bool isPasswordVisible = false;
  final testPassword = 'toggletest123';
  
  await tester.pumpWidget(
    MaterialApp(
      home: Scaffold(
        body: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              children: [
                TextFormField(
                  key: Key('password_field'),
                  obscureText: !isPasswordVisible,
                  decoration: InputDecoration(
                    labelText: 'Password',
                    suffixIcon: IconButton(
                      key: Key('toggle_button'),
                      icon: Icon(isPasswordVisible ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          isPasswordVisible = !isPasswordVisible;
                        });
                      },
                    ),
                  ),
                ),
                Text('Visible: $isPasswordVisible'), // Helper text
              ],
            );
          },
        ),
      ),
    ),
  );

  // Enter password
  await tester.enterText(find.byKey(Key('password_field')), testPassword);
  await tester.pumpAndSettle();

  // Initially hidden
  expect(find.text('Visible: false'), findsOneWidget);
  expect(find.text(testPassword), findsNothing);

  // Toggle to visible
  await tester.tap(find.byKey(Key('toggle_button')));
  await tester.pumpAndSettle();

  // Now visible
  expect(find.text('Visible: true'), findsOneWidget);
  expect(find.text(testPassword), findsOneWidget);
});
```

### 4. **Test với TextEditingController**

```dart
testWidgets('Test obscureText with controller', (WidgetTester tester) async {
  final controller = TextEditingController();
  final testPassword = 'controller123';
  
  await tester.pumpWidget(
    MaterialApp(
      home: Scaffold(
        body: TextFormField(
          key: Key('password_field'),
          controller: controller,
          obscureText: true,
          decoration: InputDecoration(labelText: 'Password'),
        ),
      ),
    ),
  );

  // Enter text
  await tester.enterText(find.byKey(Key('password_field')), testPassword);
  await tester.pumpAndSettle();

  // ✅ Controller has the actual value
  expect(controller.text, equals(testPassword));
  
  // ✅ But text is not visible in UI
  expect(find.text(testPassword), findsNothing);
});
```

## 🎯 Best Practices

### 1. **Sử dụng helper methods**

```dart
class PasswordTestHelper {
  static Future<void> enterPassword(
    WidgetTester tester, 
    String password, 
    {String fieldKey = 'password_field'}
  ) async {
    await tester.enterText(find.byKey(Key(fieldKey)), password);
    await tester.pumpAndSettle();
  }
  
  static void expectPasswordHidden(String password) {
    expect(find.text(password), findsNothing);
  }
  
  static void expectPasswordVisible(String password) {
    expect(find.text(password), findsOneWidget);
  }
}

// Usage
testWidgets('Password test with helper', (WidgetTester tester) async {
  // ... setup widget
  
  await PasswordTestHelper.enterPassword(tester, 'test123');
  PasswordTestHelper.expectPasswordHidden('test123');
});
```

### 2. **Test với Faker data**

```dart
testWidgets('ObscureText with faker passwords', (WidgetTester tester) async {
  final faker = Faker();
  
  for (int i = 0; i < 3; i++) {
    final password = faker.internet.password(length: 8 + i);
    
    await tester.pumpWidget(/* your widget */);
    
    await tester.enterText(find.byKey(Key('password')), password);
    await tester.pumpAndSettle();
    
    // Each password should be hidden
    expect(find.text(password), findsNothing);
    
    print('✅ Password ${i+1} hidden: ${password.substring(0, 3)}***');
  }
});
```

### 3. **Test edge cases**

```dart
testWidgets('ObscureText edge cases', (WidgetTester tester) async {
  await tester.pumpWidget(/* your widget */);
  
  // Test empty password
  await tester.enterText(find.byKey(Key('password')), '');
  expect(find.text(''), findsNothing);
  
  // Test special characters
  await tester.enterText(find.byKey(Key('password')), '!@#$%^&*()');
  expect(find.text('!@#$%^&*()'), findsNothing);
  
  // Test very long password
  final longPassword = 'a' * 100;
  await tester.enterText(find.byKey(Key('password')), longPassword);
  expect(find.text(longPassword), findsNothing);
});
```

## 🚫 Những gì KHÔNG nên làm

### ❌ Cố gắng access obscureText property
```dart
// ❌ SAI
final field = tester.widget<TextFormField>(finder);
expect(field.obscureText, isTrue); // Không tồn tại!
```

### ❌ Sử dụng reflection để access private fields
```dart
// ❌ SAI - Phức tạp và không reliable
import 'dart:mirrors';
// ... reflection code
```

### ❌ Test implementation details thay vì behavior
```dart
// ❌ SAI - Test implementation
expect(field.decoration.suffixIcon, isA<IconButton>());

// ✅ ĐÚNG - Test behavior
await tester.tap(find.byIcon(Icons.visibility));
expect(find.text(password), findsOneWidget);
```

## 📊 Test Coverage Checklist

- [ ] Password field hides text when obscureText = true
- [ ] Password field shows text when obscureText = false
- [ ] Visibility toggle works correctly
- [ ] Controller still contains actual password value
- [ ] Different password lengths work
- [ ] Special characters are handled
- [ ] Empty password case
- [ ] Multiple toggle operations
- [ ] Form validation with obscured text

## 🎯 Kết luận

**Nguyên tắc chính:** Test behavior (hành vi), không phải implementation (cách thực hiện).

- ✅ Test xem text có bị ẩn không
- ✅ Test xem toggle có hoạt động không  
- ✅ Test xem controller có giá trị đúng không
- ❌ Không cố access obscureText property

**Remember:** Flutter testing focuses on user experience, not internal widget properties!
