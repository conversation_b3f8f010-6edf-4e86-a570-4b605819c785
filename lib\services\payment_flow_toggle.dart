/// 🔄 Payment Flow Toggle Service
/// 
/// Service để chuyển đổi giữa Direct Payment và Web-based Payment
/// Cho phép testing và A/B testing dễ dàng
library;

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Payment flow types
enum PaymentFlowType {
  /// Traditional flow: WebView → Web → Payment App → Web → WebView
  webBased('web_based', 'Web-based Payment'),
  
  /// Direct flow: WebView → Payment App → App (direct callback)
  direct('direct', 'Direct Payment'),
  
  /// Auto-select based on conditions
  auto('auto', 'Auto Selection');

  const PaymentFlowType(this.key, this.displayName);
  
  final String key;
  final String displayName;
}

/// Payment Flow Toggle Service
class PaymentFlowToggle {
  static const String _prefKey = 'payment_flow_type';
  static const String _debugModeKey = 'payment_debug_mode';
  
  static PaymentFlowType? _cachedFlowType;
  static bool? _cachedDebugMode;
  
  /// Get current payment flow type
  static Future<PaymentFlowType> getCurrentFlowType() async {
    if (_cachedFlowType != null) {
      return _cachedFlowType!;
    }
    
    final prefs = await SharedPreferences.getInstance();
    final flowKey = prefs.getString(_prefKey);
    
    if (flowKey != null) {
      _cachedFlowType = PaymentFlowType.values.firstWhere(
        (type) => type.key == flowKey,
        orElse: () => _getDefaultFlowType(),
      );
    } else {
      _cachedFlowType = _getDefaultFlowType();
    }
    
    return _cachedFlowType!;
  }
  
  /// Set payment flow type
  static Future<void> setFlowType(PaymentFlowType flowType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_prefKey, flowType.key);
    _cachedFlowType = flowType;
    
    debugPrint('🔄 Payment flow changed to: ${flowType.displayName}');
  }
  
  /// Toggle between direct and web-based payment
  static Future<PaymentFlowType> toggleFlow() async {
    final currentFlow = await getCurrentFlowType();
    
    final newFlow = switch (currentFlow) {
      PaymentFlowType.webBased => PaymentFlowType.direct,
      PaymentFlowType.direct => PaymentFlowType.webBased,
      PaymentFlowType.auto => PaymentFlowType.direct,
    };
    
    await setFlowType(newFlow);
    return newFlow;
  }
  
  /// Check if direct payment is enabled
  static Future<bool> isDirectPaymentEnabled() async {
    final flowType = await getCurrentFlowType();
    
    return switch (flowType) {
      PaymentFlowType.direct => true,
      PaymentFlowType.webBased => false,
      PaymentFlowType.auto => _shouldUseDirectPayment(),
    };
  }
  
  /// Check if web-based payment is enabled
  static Future<bool> isWebBasedPaymentEnabled() async {
    return !(await isDirectPaymentEnabled());
  }
  
  /// Get default flow type based on environment
  static PaymentFlowType _getDefaultFlowType() {
    // In debug mode, default to direct for testing
    if (kDebugMode) {
      return PaymentFlowType.direct;
    }
    
    // In release mode, use auto selection
    return PaymentFlowType.auto;
  }
  
  /// Auto-selection logic for payment flow
  static bool _shouldUseDirectPayment() {
    // Add your logic here for auto-selection
    // For example: based on user preferences, device capabilities, etc.
    
    // For now, prefer direct payment in debug mode
    if (kDebugMode) {
      return true;
    }
    
    // In production, you might want to do A/B testing
    // or check device capabilities
    return false;
  }
  
  /// Enable debug mode for payment testing
  static Future<void> setDebugMode(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_debugModeKey, enabled);
    _cachedDebugMode = enabled;
    
    debugPrint('🐛 Payment debug mode: ${enabled ? 'ON' : 'OFF'}');
  }
  
  /// Check if debug mode is enabled
  static Future<bool> isDebugModeEnabled() async {
    if (_cachedDebugMode != null) {
      return _cachedDebugMode!;
    }
    
    final prefs = await SharedPreferences.getInstance();
    _cachedDebugMode = prefs.getBool(_debugModeKey) ?? kDebugMode;
    
    return _cachedDebugMode!;
  }
  
  /// Get flow description for UI
  static Future<String> getFlowDescription() async {
    final flowType = await getCurrentFlowType();
    final isDebug = await isDebugModeEnabled();
    
    final debugSuffix = isDebug ? ' (Debug Mode)' : '';
    
    return switch (flowType) {
      PaymentFlowType.webBased => 
        'Web-based: WebView → Web → Payment App → Web → WebView$debugSuffix',
      PaymentFlowType.direct => 
        'Direct: WebView → Payment App → App (callback)$debugSuffix',
      PaymentFlowType.auto => 
        'Auto: System decides based on conditions$debugSuffix',
    };
  }
  
  /// Reset to default settings
  static Future<void> resetToDefault() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_prefKey);
    await prefs.remove(_debugModeKey);
    
    _cachedFlowType = null;
    _cachedDebugMode = null;
    
    debugPrint('🔄 Payment flow settings reset to default');
  }
  
  /// Get all available flow types for UI selection
  static List<PaymentFlowType> getAllFlowTypes() {
    return PaymentFlowType.values;
  }
  
  /// Quick toggle function for development
  static Future<void> quickToggle() async {
    final newFlow = await toggleFlow();
    final description = await getFlowDescription();
    
    debugPrint('🚀 Quick toggle activated!');
    debugPrint('📱 New flow: ${newFlow.displayName}');
    debugPrint('📝 Description: $description');
  }
}

/// Extension for easy access in widgets
extension PaymentFlowContext on PaymentFlowToggle {
  /// Quick check for direct payment in widgets
  static Future<bool> get isDirect async {
    return await PaymentFlowToggle.isDirectPaymentEnabled();
  }
  
  /// Quick check for web-based payment in widgets
  static Future<bool> get isWebBased async {
    return await PaymentFlowToggle.isWebBasedPaymentEnabled();
  }
}

/// Payment Flow Configuration
class PaymentFlowConfig {
  /// Feature flags for different payment methods
  static const bool enableMomoDirectPayment = true;
  static const bool enableZaloPayDirectPayment = true;
  static const bool enableAirPayDirectPayment = true;
  
  /// Fallback settings
  static const bool enableFallbackToWeb = true;
  static const Duration paymentTimeout = Duration(minutes: 5);
  
  /// A/B Testing settings
  static const double directPaymentRolloutPercentage = 50.0; // 50% users
  
  /// Debug settings
  static const bool showPaymentFlowIndicator = true;
  static const bool logPaymentFlowEvents = true;
}

/// Payment Flow Events for analytics
class PaymentFlowEvents {
  static void logFlowChange(PaymentFlowType from, PaymentFlowType to) {
    if (PaymentFlowConfig.logPaymentFlowEvents) {
      debugPrint('📊 Payment flow changed: ${from.key} → ${to.key}');
    }
  }
  
  static void logDirectPaymentAttempt(String provider) {
    if (PaymentFlowConfig.logPaymentFlowEvents) {
      debugPrint('💳 Direct payment attempt: $provider');
    }
  }
  
  static void logWebPaymentFallback(String provider, String reason) {
    if (PaymentFlowConfig.logPaymentFlowEvents) {
      debugPrint('🌐 Web payment fallback: $provider - $reason');
    }
  }
}
