# 🧪 Flutter Testing Guide - Hướng dẫn Testing Flutter từ A-Z

## 📚 Mục lục
1. [Giới thiệu về Testing](#giới-thiệu)
2. [Các loại Test trong Flutter](#các-loại-test)
3. [Setup Testing Environment](#setup-testing)
4. [Unit Tests](#unit-tests)
5. [Widget Tests](#widget-tests)
6. [Integration Tests](#integration-tests)
7. [Mocking và Test Doubles](#mocking)
8. [Best Practices](#best-practices)
9. [Ví dụ thực tế: Login Screen](#ví-dụ-thực-tế)

## 🎯 Giới thiệu về Testing {#giới-thiệu}

Testing là quá trình kiểm tra xem code có hoạt động đúng như mong đợi hay không. Trong Flutter, chúng ta có 3 loại test chính:

### Tại sao cần Testing?
- ✅ **Đ<PERSON>m bảo chất lượng**: Ph<PERSON>t hiện bug sớm
- ✅ **Tự tin refactor**: Thay đổi code mà không lo phá vỡ tính năng
- ✅ **Documentation**: Test cases là tài liệu sống của code
- ✅ **Regression prevention**: Ngăn chặn bug cũ xuất hiện lại

## 🔍 Các loại Test trong Flutter {#các-loại-test}

### 1. Unit Tests 🧩
- **Mục đích**: Test logic nghiệp vụ, functions, classes riêng lẻ
- **Tốc độ**: Rất nhanh (milliseconds)
- **Scope**: Nhỏ nhất, test 1 function/method
- **Khi nào dùng**: Test business logic, validation, calculations

### 2. Widget Tests 🎨
- **Mục đích**: Test UI components, user interactions
- **Tốc độ**: Nhanh (seconds)
- **Scope**: Test 1 widget hoặc nhóm widgets
- **Khi nào dùng**: Test UI rendering, user interactions, widget behavior

### 3. Integration Tests 🔗
- **Mục đích**: Test toàn bộ app hoặc large features
- **Tốc độ**: Chậm (minutes)
- **Scope**: Lớn nhất, test end-to-end flows
- **Khi nào dùng**: Test user journeys, API integration, navigation flows

## ⚙️ Setup Testing Environment {#setup-testing}

### 1. Dependencies trong `pubspec.yaml`

```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.2
  build_runner: ^2.4.7
  http: ^1.1.0
  shared_preferences: ^2.2.2
```

### 2. Cấu trúc thư mục

```
test/
├── unit/
│   ├── models/
│   ├── services/
│   └── utils/
├── widget/
│   ├── login_widget_test.dart
│   └── register_widget_test.dart
├── integration/
│   ├── login_test.dart
│   └── user_journey_test.dart
├── mocks/
│   └── mock_api.dart
└── common.dart
```

### 3. Chạy Tests

```bash
# Chạy tất cả unit và widget tests
flutter test

# Chạy integration tests
flutter test integration_test/

# Chạy test cụ thể
flutter test test/login_test.dart

# Chạy test với coverage
flutter test --coverage
```

## 🧩 Unit Tests {#unit-tests}

Unit tests kiểm tra logic nghiệp vụ mà không cần UI.

### Ví dụ: Test Email Validation

```dart
// lib/utils/validators.dart
class Validators {
  static bool isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email);
  }
  
  static bool isValidPassword(String password) {
    return password.trim().length >= 6;
  }
}

// test/unit/validators_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_app/utils/validators.dart';

void main() {
  group('Validators Tests', () {
    group('Email Validation', () {
      test('should return true for valid emails', () {
        // Arrange
        final validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];
        
        // Act & Assert
        for (final email in validEmails) {
          expect(Validators.isValidEmail(email), isTrue, 
                 reason: '$email should be valid');
        }
      });
      
      test('should return false for invalid emails', () {
        // Arrange
        final invalidEmails = [
          'invalid-email',
          'test@',
          '@example.com',
          '',
          '<EMAIL>',
        ];
        
        // Act & Assert
        for (final email in invalidEmails) {
          expect(Validators.isValidEmail(email), isFalse,
                 reason: '$email should be invalid');
        }
      });
    });
    
    group('Password Validation', () {
      test('should return true for valid passwords', () {
        expect(Validators.isValidPassword('123456'), isTrue);
        expect(Validators.isValidPassword('password123'), isTrue);
        expect(Validators.isValidPassword('MySecurePass!'), isTrue);
      });
      
      test('should return false for invalid passwords', () {
        expect(Validators.isValidPassword('12345'), isFalse); // Too short
        expect(Validators.isValidPassword(''), isFalse); // Empty
        expect(Validators.isValidPassword('   '), isFalse); // Only spaces
      });
    });
  });
}
```

### Test Models và Data Classes

```dart
// test/unit/models/user_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_app/models/user.dart';

void main() {
  group('User Model Tests', () {
    test('should create user from JSON correctly', () {
      // Arrange
      final json = {
        'id': '123',
        'name': 'John Doe',
        'email': '<EMAIL>',
        'isActive': true,
      };
      
      // Act
      final user = User.fromJson(json);
      
      // Assert
      expect(user.id, equals('123'));
      expect(user.name, equals('John Doe'));
      expect(user.email, equals('<EMAIL>'));
      expect(user.isActive, isTrue);
    });
    
    test('should convert user to JSON correctly', () {
      // Arrange
      final user = User(
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
        isActive: true,
      );
      
      // Act
      final json = user.toJson();
      
      // Assert
      expect(json['id'], equals('123'));
      expect(json['name'], equals('John Doe'));
      expect(json['email'], equals('<EMAIL>'));
      expect(json['isActive'], isTrue);
    });
  });
}
```

## 🎨 Widget Tests {#widget-tests}

Widget tests kiểm tra UI components và user interactions.

### Ví dụ: Test Login Form

```dart
// test/widget/login_form_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_app/pages/access/login.dart';
import 'package:flutter_app/cubit/auth_cubit.dart';

// Mock classes
class MockAuthCubit extends Mock implements AuthCubit {}

void main() {
  group('Login Form Widget Tests', () {
    late MockAuthCubit mockAuthCubit;
    
    setUp(() {
      mockAuthCubit = MockAuthCubit();
    });
    
    Widget createTestWidget() {
      return MaterialApp(
        home: BlocProvider<AuthCubit>.value(
          value: mockAuthCubit,
          child: const LoginPage(),
        ),
      );
    }
    
    testWidgets('should render all form elements', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());
      
      // Assert
      expect(find.text('Đăng nhập'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email + Password
      expect(find.byType(ElevatedButton), findsAtLeastNWidgets(1));
      expect(find.text('Quên mật khẩu?'), findsOneWidget);
      expect(find.text('Đăng ký'), findsOneWidget);
    });
    
    testWidgets('should show validation errors for empty form', 
               (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act
      await tester.tap(find.text('Đăng nhập'));
      await tester.pumpAndSettle();
      
      // Assert
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
    });
    
    testWidgets('should call login when form is valid', 
               (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act
      await tester.enterText(find.byKey(const Key('email_field')), 
                            '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 
                            'password123');
      await tester.tap(find.text('Đăng nhập'));
      await tester.pumpAndSettle();
      
      // Assert
      verify(mockAuthCubit.login(any)).called(1);
    });
    
    testWidgets('should toggle password visibility', 
               (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act
      final passwordField = find.byKey(const Key('password_field'));
      final visibilityToggle = find.byIcon(Icons.visibility);
      
      // Assert initial state (password hidden)
      TextFormField passwordWidget = tester.widget(passwordField);
      expect(passwordWidget.obscureText, isTrue);
      
      // Act - toggle visibility
      await tester.tap(visibilityToggle);
      await tester.pumpAndSettle();
      
      // Assert - password visible
      passwordWidget = tester.widget(passwordField);
      expect(passwordWidget.obscureText, isFalse);
    });
  });
}
```

### Test Custom Widgets

```dart
// test/widget/custom_button_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_app/widgets/custom_button.dart';

void main() {
  group('Custom Button Widget Tests', () {
    testWidgets('should render with correct text and style', 
               (WidgetTester tester) async {
      // Arrange
      const buttonText = 'Test Button';
      bool wasPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: buttonText,
              onPressed: () => wasPressed = true,
              backgroundColor: Colors.blue,
            ),
          ),
        ),
      );
      
      // Assert
      expect(find.text(buttonText), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
      
      // Test button press
      await tester.tap(find.text(buttonText));
      expect(wasPressed, isTrue);
    });
    
    testWidgets('should be disabled when onPressed is null', 
               (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Disabled Button',
              onPressed: null, // Disabled
            ),
          ),
        ),
      );
      
      // Assert
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNull);
    });
  });
}
```
